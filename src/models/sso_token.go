package models

//import (
//	"time"
//
//	"github.com/google/uuid"
//)
//
//type Token struct {
//	Id           int64  `json:"id,omitempty"`
//	Name         string `json:"name,omitempty" description:"access token name"`
//	AccessToken  string `json:"accessToken,omitempty"`
//	RefreshToken string `json:"refreshToken,omitempty"`
//	ClientId     string `json:"clientId,omitempty"`
//	Authorize    string `json:"authorize,omitempty"`
//	Previous     string `json:"previous,omitempty"`
//	ExpiresIn    int64  `json:"expiresIn,omitempty" description:"max 3 year, default:0, max time"`
//	Scope        string `json:"scope,omitempty" description:"scope split by ' '"`
//	RedirectUri  string `json:"redirectUri,omitempty"`
//	UserName     string `json:"userName,omitempty"`
//	CreatedAt    int64  `json:"createdAt,omitempty" out:",date"`
//	LastAt       int64  `json:"lastAt,omitempty" out:",date"`
//}
//
//func (p *Token) Session() *Session {
//	now := time.Now().Unix()
//	return &Session{
//		Sid:         uuid.New().String(),
//		AccessToken: p.AccessToken,
//		Username:    p.UserName,
//		RemoteAddr:  "",
//		CreatedAt:   now,
//		UpdatedAt:   now,
//	}
//}
//
//func (p *Token) Update(cols ...string) error {
//	if _, ok := DB["sso"]; !ok {
//		return nil
//	}
//	_, err := DB["sso"].Where("access_token=?", p.AccessToken).Cols(cols...).Update(p)
//	return err
//}
//
//func TokenDelete(token string) error {
//	if _, ok := DB["sso"]; !ok {
//		return nil
//	}
//	_, err := DB["sso"].Where("access_token=?", token).Delete(new(Token))
//	return err
//}
