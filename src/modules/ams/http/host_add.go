package http

import (
	"github.com/gin-gonic/gin"

	"arboris/src/models"
)

type hostAddForm struct {
	SN            string            `json:"sn"`
	IP            string            `json:"ip" binding:"required"`
	Ident         string            `json:"ident" binding:"required"`
	Name          string            `json:"name"`
	OSVersion     string            `json:"os_version"`
	KernelVersion string            `json:"kernel_version"`
	CPUModel      string            `json:"cpu_model"`
	Cate          string            `json:"cate"`
	Note          string            `json:"note"`
	Model         string            `json:"model"`
	Manufacturer  string            `json:"manufacturer"`
	IDC           string            `json:"idc"`
	Zone          string            `json:"zone"`
	Rack          string            `json:"rack"`
	GPU           string            `json:"gpu"`
	GPUModel      string            `json:"gpu_model"`
	CPU           string            `json:"cpu"`
	Mem           string            `json:"mem"`
	Disk          string            `json:"disk"`
	Tenant        string            `json:"tenant"`
	Fields        map[string]string `json:"fields"` // 自定义字段
}

// @Summary 添加主机
// @Description 添加单台主机设备，支持基础字段和自定义字段
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param body body hostAddForm true "主机添加参数"
// @Success 200 {object} ApiResponse{dat=models.Host} "创建的主机信息"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 409 {object} ApiResponse "IP或标识符已存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/ams-ce/host [post]
func hostAdd(c *gin.Context) {
	var f hostAddForm
	bind(c, &f)

	// 检查IP和Ident是否已存在
	exists, err := models.HostGet("ip=? or ident=?", f.IP, f.Ident)
	dangerous(err)

	if exists != nil {
		if exists.IP == f.IP {
			bomb("IP %s already exists", f.IP)
		}
		if exists.Ident == f.Ident {
			bomb("ident %s already exists", f.Ident)
		}
	}

	// 获取所有可用的自定义字段定义
	hostFields, err := models.HostFieldGets()
	dangerous(err)

	// 创建字段映射，用于验证
	fieldMap := make(map[string]models.HostField)
	for _, field := range hostFields {
		fieldMap[field.FieldIdent] = field
	}

	// 验证必填的自定义字段
	for _, field := range hostFields {
		if field.FieldRequired == 1 {
			if value, exists := f.Fields[field.FieldIdent]; !exists || value == "" {
				bomb("required field %s(%s) is empty", field.FieldName, field.FieldIdent)
			}
		}
	}

	// 验证提供的自定义字段是否都是合法的
	for fieldIdent := range f.Fields {
		if _, exists := fieldMap[fieldIdent]; !exists {
			bomb("field %s not defined", fieldIdent)
		}
	}

	// 准备基本字段
	fields := make(map[string]interface{})
	if f.CPU != "" {
		fields["cpu"] = f.CPU
	}
	if f.CPUModel != "" {
		fields["cpu_model"] = f.CPUModel
	}
	if f.Mem != "" {
		fields["mem"] = f.Mem
	}
	if f.Disk != "" {
		fields["disk"] = f.Disk
	}
	if f.GPU != "" {
		fields["gpu"] = f.GPU
	}
	if f.GPUModel != "" {
		fields["gpu_model"] = f.GPUModel
	}
	if f.Model != "" {
		fields["model"] = f.Model
	}
	if f.IDC != "" {
		fields["idc"] = f.IDC
	}
	if f.Zone != "" {
		fields["zone"] = f.Zone
	}
	if f.Rack != "" {
		fields["rack"] = f.Rack
	}
	if f.OSVersion != "" {
		fields["os_version"] = f.OSVersion
	}
	if f.KernelVersion != "" {
		fields["kernel_version"] = f.KernelVersion
	}
	if f.Manufacturer != "" {
		fields["manufacturer"] = f.Manufacturer
	}
	if f.Note != "" {
		fields["note"] = f.Note
	}

	// 创建主机
	host, err := models.HostNew(f.SN, f.IP, f.Ident, f.Name, f.Cate, fields)
	dangerous(err)

	// 如果指定了租户，更新租户信息
	if f.Tenant != "" {
		err = models.HostUpdateTenant([]int64{host.Id}, f.Tenant)
		dangerous(err)
	}

	// 添加自定义字段值
	if len(f.Fields) > 0 {
		fieldValues := make([]models.HostFieldValue, 0, len(f.Fields))
		for fieldIdent, value := range f.Fields {
			if value != "" {
				fieldValues = append(fieldValues, models.HostFieldValue{
					HostId:     host.Id,
					FieldIdent: fieldIdent,
					FieldValue: value,
				})
			}
		}

		if len(fieldValues) > 0 {
			err = models.HostFieldValuePuts(host.Id, fieldValues)
			dangerous(err)
		}
	}

	renderData(c, host, nil)
}
