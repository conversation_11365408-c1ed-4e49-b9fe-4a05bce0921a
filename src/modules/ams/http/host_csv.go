package http

import (
	jsEvents "arboris/src/modules/jumpserver-sync/events"
	rdbEvents "arboris/src/modules/rdb/events"
	"bytes"
	"crypto/rand"
	"encoding/csv"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"

	"arboris/src/models"
)

/*
数据库表结构 - 导入失败数据存储

CREATE TABLE `host_import_failed_data` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `import_id` varchar(64) NOT NULL COMMENT '导入批次ID',
  `row_num` int NOT NULL COMMENT '原始CSV行号',
  `row_data` text NOT NULL COMMENT '原始行数据JSON',
  `error_msg` varchar(1024) NOT NULL COMMENT '失败原因',
  `created_at` bigint NOT NULL COMMENT '创建时间戳',
  `expires_at` bigint NOT NULL COMMENT '过期时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_import_id` (`import_id`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主机导入失败数据表';
*/

// 定时清理过期数据的互斥锁
var cleanupMutex sync.Mutex

// 启动定时清理任务
func init() {
	go func() {
		ticker := time.NewTicker(1 * time.Hour) // 每小时清理一次
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				cleanupExpiredFailedData()
			}
		}
	}()
}

// 清理过期的失败数据
func cleanupExpiredFailedData() {
	cleanupMutex.Lock()
	defer cleanupMutex.Unlock()

	now := time.Now().Unix()
	_, err := models.DB["ams"].Exec("DELETE FROM host_import_failed_data WHERE expires_at < ?", now)
	if err != nil {
		// 记录错误但不中断程序
		fmt.Printf("Failed to cleanup expired failed data: %v\n", err)
	}
}

// 存储失败数据到数据库
func storeFailedData(importId string, failedRows []FailedRowData) error {
	if len(failedRows) == 0 {
		return nil
	}

	session := models.DB["ams"].NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return fmt.Errorf("failed to start transaction: %v", err)
	}

	now := time.Now().Unix()
	expiresAt := now + 24*60*60 // 24小时后过期

	// 批量准备数据
	records := make([]*HostImportFailedData, len(failedRows))
	for i, failedRow := range failedRows {
		// 将行数据转换为JSON字符串
		rowDataBytes, err := json.Marshal(failedRow.Data)
		if err != nil {
			session.Rollback()
			return fmt.Errorf("failed to marshal row data: %v", err)
		}

		records[i] = &HostImportFailedData{
			ImportId:  importId,
			RowNum:    failedRow.RowNum,
			RowData:   string(rowDataBytes),
			ErrorMsg:  failedRow.Error,
			CreatedAt: now,
			ExpiresAt: expiresAt,
		}
	}

	// 批量插入
	if _, err := session.Insert(records); err != nil {
		session.Rollback()
		return fmt.Errorf("failed to batch insert failed data: %v", err)
	}

	return session.Commit()
}

// 从数据库获取失败数据
func getFailedData(importId string) ([]FailedRowData, bool) {
	// 先清理过期数据
	cleanupExpiredFailedData()

	var records []HostImportFailedData
	err := models.DB["ams"].Where("import_id = ?", importId).Find(&records)
	if err != nil {
		fmt.Printf("Failed to get failed data: %v\n", err)
		return nil, false
	}

	if len(records) == 0 {
		return nil, false
	}

	failedRows := make([]FailedRowData, len(records))
	for i, record := range records {
		var rowData []string
		if err := json.Unmarshal([]byte(record.RowData), &rowData); err != nil {
			fmt.Printf("Failed to unmarshal row data: %v\n", err)
			continue
		}

		failedRows[i] = FailedRowData{
			RowNum: record.RowNum,
			Data:   rowData,
			Error:  record.ErrorMsg,
		}
	}

	return failedRows, true
}

// 生成导入ID
func generateImportId() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// ImportResult CSV导入结果
type ImportResult struct {
	SuccessCount int    `json:"success_count" description:"成功导入数量"`
	FailedCount  int    `json:"failed_count" description:"失败数量"`
	TotalRows    int    `json:"total_rows" description:"总行数"`
	ImportId     string `json:"import_id,omitempty" description:"导入批次ID，用于下载失败数据。仅在有失败数据时返回"`
}

// FailedRowData 失败行数据
type FailedRowData struct {
	RowNum int      `json:"row_num" description:"行号"`
	Data   []string `json:"data" description:"行数据"`
	Error  string   `json:"error" description:"错误信息"`
}

// HostImportFailedData 数据库模型
type HostImportFailedData struct {
	Id        int64  `json:"id" xorm:"'id' pk autoincr"`
	ImportId  string `json:"import_id" xorm:"'import_id'"`
	RowNum    int    `json:"row_num" xorm:"'row_num'"`
	RowData   string `json:"row_data" xorm:"'row_data'"`
	ErrorMsg  string `json:"error_msg" xorm:"'error_msg'"`
	CreatedAt int64  `json:"created_at" xorm:"'created_at'"`
	ExpiresAt int64  `json:"expires_at" xorm:"'expires_at'"`
}

// @Summary 下载主机导入模板
// @Description 动态根据自定义字段生成主机导入模板文件，支持CSV和Excel格式
// @Tags 主机管理
// @Accept json
// @Produce application/octet-stream
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param format query string false "文件格式" Enums(csv,xlsx) default(csv)
// @Success 200 {file} file "模板文件"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/ams-ce/hosts/csv/template [get]
func GetHostCSVTemplate(c *gin.Context) {
	format := strings.ToLower(c.DefaultQuery("format", "csv"))

	headers, err := generateHeaders()
	dangerous(err)

	example := []string{
		"SN001",
		"*************",
		"*************",
		"Web-server-01",
		"CentOS Linux 7 (Core)",
		"3.10.0-1160.el7.x86_64",
		"Intel Xeon E5-2680 v4",
		"2.40GHz * 2",
		"64GB DDR4",
		"SSD 500GB + HDD 2TB",
		"host",
		"inner",
		"8",
		"NVIDIA Tesla V100 * 2",
		"Dell PowerEdge R740",
		"北京机房",
		"可用区1",
		"A12",
		"Dell",
		"备注信息",
	}
	// 为自定义字段添加示例值
	customFieldCount := len(headers) - 20 // 基础字段
	for i := 0; i < customFieldCount; i++ {
		example = append(example, "示例值")
	}

	switch format {
	case "xlsx":
		generateExcelTemplate(c, headers, example)
	case "csv":
		generateCSVTemplate(c, headers, example)
	default:
		bomb("unsupported format '%s'. Only 'csv' and 'xlsx' are supported", format)
	}
}

// generateCSVTemplate 生成CSV模板
func generateCSVTemplate(c *gin.Context, headers, example []string) {
	buf := new(bytes.Buffer)
	// 添加 UTF-8 BOM 以确保中文字符在 Excel 中正确显示
	buf.Write([]byte{0xEF, 0xBB, 0xBF})
	writer := csv.NewWriter(buf)

	if err := writer.Write(headers); err != nil {
		bomb("failed to write CSV headers: %v", err)
	}

	if err := writer.Write(example); err != nil {
		bomb("failed to write example row: %v", err)
	}

	writer.Flush()

	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename=host_template.csv; filename*=UTF-8''%E4%B8%BB%E6%9C%BA%E6%A8%A1%E6%9D%BF.csv")
	c.Header("Content-Type", "text/csv; charset=utf-8")
	c.Header("Cache-Control", "no-cache")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Data(http.StatusOK, "application/octet-stream", buf.Bytes())
}

// generateExcelTemplate 生成Excel模板
func generateExcelTemplate(c *gin.Context, headers, example []string) {
	f := excelize.NewFile()
	defer f.Close()

	// 设置工作表名称
	sheetName := "主机导入模板"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		bomb("failed to create Excel sheet: %v", err)
	}

	// 删除默认的Sheet1
	f.DeleteSheet("Sheet1")

	// 设置活动工作表
	f.SetActiveSheet(index)

	// 写入标题行
	for i, header := range headers {
		cell := fmt.Sprintf("%s1", string(rune('A'+i)))
		f.SetCellValue(sheetName, cell, header)
	}

	// 写入示例行
	for i, value := range example {
		cell := fmt.Sprintf("%s2", string(rune('A'+i)))
		f.SetCellValue(sheetName, cell, value)
	}

	// 设置标题行样式
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#E0E0E0"},
			Pattern: 1,
		},
	})
	if err == nil {
		f.SetRowStyle(sheetName, 1, 1, style)
	}

	// 自动调整列宽
	for i := range headers {
		col := string(rune('A' + i))
		f.SetColWidth(sheetName, col, col, 15)
	}

	// 生成文件内容
	buf, err := f.WriteToBuffer()
	if err != nil {
		bomb("failed to generate Excel file: %v", err)
	}

	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename=host_template.xlsx; filename*=UTF-8''%E4%B8%BB%E6%9C%BA%E6%A8%A1%E6%9D%BF.xlsx")
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Cache-Control", "no-cache")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Data(http.StatusOK, "application/octet-stream", buf.Bytes())
}

// parseCSVFile 解析CSV文件
func parseCSVFile(file *multipart.FileHeader) ([][]string, error) {
	f, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open CSV file: %v", err)
	}
	defer f.Close()

	reader := csv.NewReader(f)
	records, err := reader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("failed to parse CSV file: %v", err)
	}

	return records, nil
}

// parseExcelFile 解析Excel文件（支持.xls和.xlsx）
func parseExcelFile(file *multipart.FileHeader) ([][]string, error) {
	f, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open Excel file: %v", err)
	}
	defer f.Close()

	// 创建临时文件来存储上传的Excel文件
	tempFile, err := os.CreateTemp("", "excel_import_*.xlsx")
	if err != nil {
		return nil, fmt.Errorf("failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	// 将上传的文件内容复制到临时文件
	_, err = io.Copy(tempFile, f)
	if err != nil {
		return nil, fmt.Errorf("failed to copy file content: %v", err)
	}

	// 使用excelize打开Excel文件
	xlsx, err := excelize.OpenFile(tempFile.Name())
	if err != nil {
		return nil, fmt.Errorf("failed to open Excel file: %v", err)
	}
	defer xlsx.Close()

	// 获取第一个工作表
	sheets := xlsx.GetSheetList()
	if len(sheets) == 0 {
		return nil, fmt.Errorf("no sheets found in Excel file")
	}

	// 读取第一个工作表的所有行
	rows, err := xlsx.GetRows(sheets[0])
	if err != nil {
		return nil, fmt.Errorf("failed to read Excel rows: %v", err)
	}

	return rows, nil
}

// generateHeaders 生成统一的CSV头部，确保模板下载和失败数据下载保持一致
func generateHeaders() ([]string, error) {
	fields, err := models.HostFieldGets()
	if err != nil {
		return nil, err
	}

	headers := []string{
		"序列号", "IP地址", "标识符", "主机名",
		"操作系统版本", "内核版本", "CPU型号", "CPU核数", "内存", "磁盘",
		"类别", "租户",
		"GPU卡数", "GPU型号", "型号", "地域", "可用区", "机架", "厂商", "备注",
	}
	for _, field := range fields {
		headers = append(headers, field.FieldIdent)
	}
	return headers, nil
}

// parseFileByExtension 根据文件扩展名选择解析方法
func parseFileByExtension(file *multipart.FileHeader) ([][]string, error) {
	filename := file.Filename
	ext := strings.ToLower(filepath.Ext(filename))

	switch ext {
	case ".csv":
		return parseCSVFile(file)
	case ".xls", ".xlsx":
		return parseExcelFile(file)
	default:
		return nil, fmt.Errorf("unsupported file format '%s'. Only CSV (.csv), Excel (.xls, .xlsx) files are supported", ext)
	}
}

// @Summary 批量导入主机
// @Description 通过CSV或Excel文件批量导入主机数据，支持基础字段和自定义字段。支持.csv、.xls、.xlsx格式。最大支持1000行数据导入。可选择将导入的主机挂载到指定节点。返回导入结果统计信息，详细错误信息可通过失败数据下载接口获取
// @Tags 主机管理
// @Accept multipart/form-data
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param file formData file true "CSV或Excel文件（支持.csv、.xls、.xlsx格式，最大1000行数据，不包括标题行）"
// @Param node_ids formData string false "节点ID列表，用逗号分隔。如果指定，导入的主机将自动挂载到这些节点下"
// @Success 200 {object} ApiResponse{dat=ImportResult} "导入完成，返回统计信息。如果有失败记录，err字段会包含错误提示，import_id用于下载失败数据"
// @Failure 400 {object} ApiResponse "请求参数错误（如文件格式错误、超过1000行限制等）"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/ams-ce/hosts/csv/import [post]
func ImportHostsFromCSV(c *gin.Context) {
	// 设置超时和响应头
	c.Header("Content-Type", "application/json")

	// 解析node_ids参数（可选）
	var nodeIds []int64
	nodeIdsStr := c.PostForm("node_ids")
	if nodeIdsStr != "" {
		nodeIdStrs := strings.Split(nodeIdsStr, ",")
		for _, nodeIdStr := range nodeIdStrs {
			nodeIdStr = strings.TrimSpace(nodeIdStr)
			if nodeIdStr != "" {
				nodeId, err := strconv.ParseInt(nodeIdStr, 10, 64)
				if err != nil {
					bomb("invalid node_id: %s", nodeIdStr)
				}
				nodeIds = append(nodeIds, nodeId)
			}
		}
	}

	file, err := c.FormFile("file")
	if err != nil {
		bomb("no file uploaded")
	}

	// 检查文件大小限制 (例如 10MB)
	if file.Size > 10*1024*1024 {
		bomb("file too large, maximum size is 10MB")
	}

	// 根据文件扩展名解析文件
	records, err := parseFileByExtension(file)
	if err != nil {
		bomb(err.Error())
	}

	if len(records) < 2 { // Need at least header and one data row
		bomb("CSV file must contain at least one data row")
	}

	// 限制最大导入行数为1000行（不包括标题行）
	dataRows := len(records) - 1
	if dataRows > 1000 {
		bomb("Too many rows to import. Maximum allowed: 1000 rows, but got: %d rows", dataRows)
	}
	// Get headers
	headers := records[0]
	headerMap := make(map[string]int)
	for i, header := range headers {
		headerMap[strings.TrimSpace(header)] = i
	}

	hostFields, err := models.HostFieldGets()
	if err != nil {
		bomb("Failed to get host fields: %v", err)
	}

	fieldMap := make(map[string]models.HostField)
	for _, field := range hostFields {
		fieldMap[field.FieldIdent] = field
	}

	// 批量处理以提高性能
	var errors []string
	var successCount int
	var failedRows []FailedRowData
	var importId string

	// 预处理和验证所有数据
	type hostData struct {
		host        *models.Host
		fieldValues []models.HostFieldValue
		rowNum      int
	}

	validHosts := make([]hostData, 0)
	allIPs := make([]string, 0)
	allIdents := make([]string, 0)

	// 用于检查CSV内部重复的映射
	csvIPMap := make(map[string]int)    // IP -> 行号
	csvIdentMap := make(map[string]int) // Ident -> 行号

	// 数据预处理
	for i, record := range records[1:] {
		rowNum := i + 2

		// Skip empty rows
		if len(record) == 0 || (len(record) == 1 && strings.TrimSpace(record[0]) == "") {
			continue
		}

		// Ensure record has enough columns
		for len(record) < len(headers) {
			record = append(record, "")
		}

		// Extract basic host fields - support both Chinese and English headers
		getSafeValue := func(chineseKey, englishKey string) string {
			if idx, exists := headerMap[chineseKey]; exists {
				return strings.TrimSpace(record[idx])
			}
			if idx, exists := headerMap[englishKey]; exists {
				return strings.TrimSpace(record[idx])
			}
			return ""
		}

		ip := getSafeValue("IP地址", "ip")
		ident := getSafeValue("标识符", "ident")

		// Basic validation
		if ip == "" || ident == "" {
			errorMsg := fmt.Sprintf("Row %d: IP and Ident are required", rowNum)
			errors = append(errors, errorMsg)
			failedRows = append(failedRows, FailedRowData{
				RowNum: rowNum,
				Data:   record,
				Error:  errorMsg,
			})
			continue
		}

		// 检查CSV内部重复
		if existingRow, exists := csvIPMap[ip]; exists {
			errorMsg := fmt.Sprintf("Row %d: IP %s already exists in row %d", rowNum, ip, existingRow)
			errors = append(errors, errorMsg)
			failedRows = append(failedRows, FailedRowData{
				RowNum: rowNum,
				Data:   record,
				Error:  errorMsg,
			})
			continue
		}
		if existingRow, exists := csvIdentMap[ident]; exists {
			errorMsg := fmt.Sprintf("Row %d: Ident %s already exists in row %d", rowNum, ident, existingRow)
			errors = append(errors, errorMsg)
			failedRows = append(failedRows, FailedRowData{
				RowNum: rowNum,
				Data:   record,
				Error:  errorMsg,
			})
			continue
		}

		// 记录IP和Ident
		csvIPMap[ip] = rowNum
		csvIdentMap[ident] = rowNum

		host := &models.Host{
			SN:            getSafeValue("序列号", "sn"),
			IP:            ip,
			Ident:         ident,
			Name:          getSafeValue("主机名", "name"),
			OSVersion:     getSafeValue("操作系统版本", "os_version"),
			KernelVersion: getSafeValue("内核版本", "kernel_version"),
			CPUModel:      getSafeValue("CPU型号", "cpu_model"),
			CPU:           getSafeValue("CPU", "cpu"),
			Mem:           getSafeValue("内存", "mem"),
			Disk:          getSafeValue("磁盘", "disk"),
			Note:          getSafeValue("备注", "note"),
			Cate:          getSafeValue("分类", "cate"),
			Tenant:        getSafeValue("租户", "tenant"),
			GPU:           getSafeValue("GPU", "gpu"),
			GPUModel:      getSafeValue("GPU型号", "gpu_model"),
			Model:         getSafeValue("型号", "model"),
			IDC:           getSafeValue("机房", "idc"),
			Zone:          getSafeValue("可用区", "zone"),
			Rack:          getSafeValue("机架", "rack"),
			Manufacturer:  getSafeValue("厂商", "manufacturer"),
			Clock:         time.Now().Unix(),
		}

		// Process custom fields
		fieldValues := make([]models.HostFieldValue, 0)
		hasRequiredFieldError := false

		for fieldIdent, field := range fieldMap {
			if idx, exists := headerMap[fieldIdent]; exists {
				value := strings.TrimSpace(record[idx])

				// Validate required fields
				if field.FieldRequired == 1 && value == "" {
					errorMsg := fmt.Sprintf("Row %d: Required field %s is empty", rowNum, fieldIdent)
					errors = append(errors, errorMsg)
					failedRows = append(failedRows, FailedRowData{
						RowNum: rowNum,
						Data:   record,
						Error:  errorMsg,
					})
					hasRequiredFieldError = true
					break
				}

				if value != "" {
					fieldValues = append(fieldValues, models.HostFieldValue{
						FieldIdent: fieldIdent,
						FieldValue: value,
					})
				}
			}
		}

		if hasRequiredFieldError {
			continue
		}

		validHosts = append(validHosts, hostData{
			host:        host,
			fieldValues: fieldValues,
			rowNum:      rowNum,
		})
		allIPs = append(allIPs, ip)
		allIdents = append(allIdents, ident)
	}

	// 批量检查重复
	var duplicateMap map[string]bool
	if len(allIPs) > 0 {
		// 对于小批量数据（<1000条），直接一次性查询，避免分批的开销
		if len(allIPs) <= 1000 {
			var err error
			duplicateMap, err = models.HostCheckDuplicates(allIPs, allIdents)
			if err != nil {
				c.JSON(500, gin.H{"err": fmt.Sprintf("Failed to check duplicates: %v", err), "dat": nil})
				return
			}
		} else {
			// 大批量数据才分批处理
			const batchSize = 1000
			duplicateMap = make(map[string]bool)

			for i := 0; i < len(allIPs); i += batchSize {
				end := i + batchSize
				if end > len(allIPs) {
					end = len(allIPs)
				}

				batchIPs := allIPs[i:end]
				batchIdents := allIdents[i:end]

				batchDuplicateMap, err := models.HostCheckDuplicates(batchIPs, batchIdents)
				if err != nil {
					c.JSON(500, gin.H{"err": fmt.Sprintf("Failed to check duplicates: %v", err), "dat": nil})
					return
				}

				// 合并结果
				for k, v := range batchDuplicateMap {
					duplicateMap[k] = v
				}
			}
		}

		// 过滤掉重复的主机
		filteredHosts := make([]hostData, 0)
		for _, hostData := range validHosts {
			if duplicateMap[hostData.host.IP] || duplicateMap[hostData.host.Ident] {
				errorMsg := fmt.Sprintf("Row %d: Duplicate IP or Ident found", hostData.rowNum)
				errors = append(errors, errorMsg)
				// 需要从原始记录中获取数据
				if hostData.rowNum-2 < len(records)-1 {
					failedRows = append(failedRows, FailedRowData{
						RowNum: hostData.rowNum,
						Data:   records[hostData.rowNum-1], // rowNum是从2开始的，所以减1
						Error:  errorMsg,
					})
				}
				continue
			}
			filteredHosts = append(filteredHosts, hostData)
		}
		validHosts = filteredHosts

	}

	// 批量插入
	if len(validHosts) > 0 {
		// 对于小批量数据，使用更大的批量大小以减少事务开销
		insertBatchSize := 200
		if len(validHosts) <= 50 {
			insertBatchSize = len(validHosts) // 小批量直接一次性插入
		}

		// 分批插入主机数据
		for i := 0; i < len(validHosts); i += insertBatchSize {
			end := i + insertBatchSize
			if end > len(validHosts) {
				end = len(validHosts)
			}

			batchHosts := validHosts[i:end]
			hostsToInsert := make([]*models.Host, len(batchHosts))
			for j, hostData := range batchHosts {
				hostsToInsert[j] = hostData.host
			}

			// 使用优化的批量插入方法
			err := models.HostBatchNew(hostsToInsert)
			if err != nil {
				c.JSON(500, gin.H{"err": fmt.Sprintf("Failed to batch insert hosts: %v", err), "dat": nil})
				return
			}

			// 批量插入自定义字段值
			allFieldValues := make([]models.HostFieldValue, 0)
			for j, hostData := range batchHosts {
				if j < len(hostsToInsert) {
					hostId := hostsToInsert[j].Id
					for _, fv := range hostData.fieldValues {
						fv.HostId = hostId
						allFieldValues = append(allFieldValues, fv)
					}
				}
			}

			if len(allFieldValues) > 0 {
				session := models.DB["ams"].NewSession()
				defer session.Close()

				if err := session.Begin(); err != nil {
					c.JSON(500, gin.H{"err": fmt.Sprintf("Failed to start field values transaction: %v", err), "dat": nil})
					return
				}

				_, err := session.Insert(allFieldValues)
				if err != nil {
					session.Rollback()
					c.JSON(500, gin.H{"err": fmt.Sprintf("Failed to batch insert field values: %v", err), "dat": nil})
					return
				}

				if err := session.Commit(); err != nil {
					c.JSON(500, gin.H{"err": fmt.Sprintf("Failed to commit field values transaction: %v", err), "dat": nil})
					return
				}
			}

			successCount += len(hostsToInsert)

			//// 发布主机创建事件到JumpServer同步
			//for _, host := range hostsToInsert {
			//	go publishHostCreateEvent(host)
			//}

			// 如果指定了节点，将主机挂载到节点并发布资源绑定事件
			if len(nodeIds) > 0 {
				// 为每个主机创建资源并绑定到指定节点
				for _, host := range hostsToInsert {
					// 创建资源
					uuid := fmt.Sprintf("host-%d", host.Id)
					resource := &models.Resource{
						UUID:       uuid,
						Ident:      host.Ident,
						Name:       host.Name,
						Cate:       "host",
						Tenant:     host.Tenant,
						SourceId:   host.Id,
						SourceType: "host",
					}

					// 构建extend字段
					fields := map[string]interface{}{
						"cpu":   host.CPU,
						"mem":   host.Mem,
						"disk":  host.Disk,
						"gpu":   host.GPU,
						"model": host.Model,
						"idc":   host.IDC,
					}

					extendBytes, err := json.Marshal(fields)
					if err != nil {
						log.Printf("Failed to marshal extend fields for host %s: %v", host.Ident, err)
						continue
					}
					resource.Extend = string(extendBytes)

					// 保存资源
					err = resource.Save()
					if err != nil {
						log.Printf("Failed to create resource for host %s: %v", host.Ident, err)
						continue
					}

					// 绑定到所有指定的节点
					for _, nodeId := range nodeIds {
						// 验证节点是否存在
						node, err := models.NodeGet("id=?", nodeId)
						if err != nil || node == nil {
							log.Printf("Node %d not found, skipping bind for host %s", nodeId, host.Ident)
							continue
						}

						// 绑定资源到节点
						err = models.NodeResourceBind(nodeId, resource.Id)
						if err != nil {
							log.Printf("Failed to bind resource %d to node %d: %v", resource.Id, nodeId, err)
							continue
						}

						// 发布资源绑定事件到JumpServer同步
						go publishResourceBindEventForCSV(nodeId, resource.Id)
					}
				}
			}
		}
	}

	// Return response
	totalRows := len(records) - 1 // 减去标题行
	failedCount := totalRows - successCount

	if len(errors) > 0 {
		// 生成导入ID并存储失败数据
		importId = generateImportId()
		if err := storeFailedData(importId, failedRows); err != nil {
			// 即使存储失败数据出错，也要返回导入结果，但不提供下载功能
			importId = ""
		}

		response := gin.H{
			"success_count": successCount,
			"failed_count":  failedCount,
			"total_rows":    totalRows,
		}

		if importId != "" {
			response["import_id"] = importId
		}

		c.JSON(200, gin.H{
			"err": "some rows failed to import",
			"dat": response,
		})
		return
	}

	// 全部成功的情况
	c.JSON(200, gin.H{
		"err": nil,
		"dat": gin.H{
			"success_count": successCount,
			"failed_count":  0,
			"total_rows":    totalRows,
		},
	})
}

// @Summary 下载导入失败的数据
// @Description 根据导入批次ID下载导入失败的CSV数据，包含原始数据和错误信息
// @Tags 主机管理
// @Accept json
// @Produce application/octet-stream
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param import_id path string true "导入批次ID"
// @Success 200 {file} file "失败数据CSV文件"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "导入批次不存在或已过期"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/ams-ce/hosts/csv/failed/{import_id} [get]
func DownloadFailedData(c *gin.Context) {
	importId := c.Param("import_id")
	if importId == "" {
		bomb("import_id is required")
	}

	// 清理过期数据
	cleanupExpiredFailedData()

	// 获取失败数据
	failedRows, exists := getFailedData(importId)
	if !exists {
		bomb("import batch not found or expired")
	}

	if len(failedRows) == 0 {
		bomb("no failed data found")
	}

	// 获取统一的头部信息
	headers, err := generateHeaders()
	dangerous(err)

	headers = append(headers, "错误信息") // 添加错误信息列

	buf := new(bytes.Buffer)
	// 添加 UTF-8 BOM 以确保中文字符在 Excel 中正确显示
	buf.Write([]byte{0xEF, 0xBB, 0xBF})
	writer := csv.NewWriter(buf)

	if err := writer.Write(headers); err != nil {
		bomb("failed to write CSV headers: %v", err)
	}

	// 写入失败的数据行
	for _, failedRow := range failedRows {
		// 确保数据行有足够的列
		row := make([]string, len(headers))
		for i, data := range failedRow.Data {
			if i < len(headers)-1 { // 最后一列是错误信息
				row[i] = data
			}
		}
		// 添加错误信息到最后一列
		row[len(headers)-1] = failedRow.Error

		if err := writer.Write(row); err != nil {
			bomb("failed to write failed row: %v", err)
		}
	}

	writer.Flush()

	// 设置响应头
	filename := fmt.Sprintf("failed_import_%s.csv", importId[:8])
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s; filename*=UTF-8''%s", filename, filename))
	c.Header("Content-Type", "text/csv; charset=utf-8")
	c.Header("Cache-Control", "no-cache")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Data(http.StatusOK, "application/octet-stream", buf.Bytes())
}

// publishHostCreateEvent 发布主机创建事件
func publishHostCreateEvent(host *models.Host) {
	if !rdbEvents.IsJSEnabled() {
		return
	}

	hostData := &jsEvents.HostEventData{
		ID:            host.Id,
		SN:            host.SN,
		IP:            host.IP,
		Ident:         host.Ident,
		Name:          host.Name,
		OSVersion:     host.OSVersion,
		KernelVersion: host.KernelVersion,
		CPUModel:      host.CPUModel,
		CPU:           host.CPU,
		Mem:           host.Mem,
		Disk:          host.Disk,
		Note:          host.Note,
		Cate:          host.Cate,
		Tenant:        host.Tenant,
		Clock:         host.Clock,
		GPU:           host.GPU,
		GPUModel:      host.GPUModel,
		Model:         host.Model,
		IDC:           host.IDC,
		Zone:          host.Zone,
		Rack:          host.Rack,
		Manufacturer:  host.Manufacturer,
	}

	if err := rdbEvents.GetJSProducer().PublishHostEvent(jsEvents.EventHostCreate, hostData); err != nil {
		log.Printf("Failed to publish host create event for host %s: %v", host.Ident, err)
	} else {
		log.Printf("Published host create event for CSV imported host: %s", host.Ident)
	}
}

// publishResourceBindEventForCSV 发布资源绑定事件（CSV导入专用）
func publishResourceBindEventForCSV(nodeId, resourceId int64) {
	if !rdbEvents.IsJSEnabled() {
		return
	}

	// 获取节点和资源信息
	node, err := models.NodeGet("id=?", nodeId)
	if err != nil || node == nil {
		log.Printf("Failed to get node %d for resource bind event: %v", nodeId, err)
		return
	}

	resource, err := models.ResourceGet("id=?", resourceId)
	if err != nil || resource == nil {
		log.Printf("Failed to get resource %d for resource bind event: %v", resourceId, err)
		return
	}

	bindData := &jsEvents.ResourceBindEventData{
		NodeID:       nodeId,
		ResourceID:   resourceId,
		NodePath:     node.Path,
		ResourceUUID: resource.UUID,
	}

	if err := rdbEvents.GetJSProducer().PublishResourceBindEvent(jsEvents.EventResourceBind, bindData); err != nil {
		log.Printf("Failed to publish resource bind event for node %d, resource %d: %v", nodeId, resourceId, err)
	} else {
		log.Printf("Published resource bind event for CSV import: node=%d, resource=%d", nodeId, resourceId)
	}
}
