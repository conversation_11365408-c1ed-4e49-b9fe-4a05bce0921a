package http

import (
	"arboris/src/models"
	"arboris/src/modules/ams/config"
	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/logger"
	"net/http"
)

func shouldBeLogin() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("username", mustUsername(c))
		c.Next()
	}
}

func mustUsername(c *gin.Context) string {
	username := sessionUsername(c)
	if username == "" {
		username = headerUsername(c)
	}

	if username == "" {
		bomb("unauthorized")
	}

	return username
}

func sessionUsername(c *gin.Context) string {
	sess, err := models.SessionGetWithCache(readSessionId(c))
	if err != nil {
		return ""
	}
	return sess.Username
}

func headerUsername(c *gin.Context) string {
	token := c.GetHeader("X-User-Token")
	if token == "" {
		return ""
	}

	ut, err := models.UserTokenGet("token=?", token)
	if err != nil {
		logger.Warningf("UserTokenGet[%s] fail: %v", token, err)
		return ""
	}

	if ut == nil {
		return ""
	}

	return ut.Username
}

// CORS 中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 允许所有来源
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-User-Token")
		c.Header("Access-Control-Expose-Headers", "Content-Length")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

func readSessionId(c *gin.Context) string {
	sid, err := c.Cookie(config.Config.HTTP.CookieName)
	if err != nil {
		return ""
	}
	return sid
}
