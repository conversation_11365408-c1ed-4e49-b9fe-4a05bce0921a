package http

import (
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	_ "arboris/docs-ams" // 导入生成的docs包
)

func Config(r *gin.Engine) {
	// 添加 CORS 中间件
	r.Use(corsMiddleware())

	// Swagger 文档路由 - 启用认证信息持久化，默认展开所有分类
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler,
		ginSwagger.PersistAuthorization(true),
		ginSwagger.DocExpansion("list"),
	))

	notLogin := r.Group("/api/ams-ce")
	{
		notLogin.GET("/ping", ping)
	}

	userLogin := r.Group("/api/ams-ce").Use(shouldBeLogin())
	{
		userLogin.GET("/hosts", hostGets)
		userLogin.POST("/host", hostAdd)
		userLogin.GET("/host/:id", hostGet)
		userLogin.PUT("/hosts/tenant", hostTenantPut)
		userLogin.PUT("/hosts/node", hostNodePut)
		userLogin.PUT("/hosts/note", hostNotePut)
		userLogin.PUT("/hosts/cate", hostCatePut)
		userLogin.DELETE("/hosts", hostDel)
		userLogin.POST("/hosts/fields", hostFieldNew)
		userLogin.GET("/hosts/fields", hostFieldsGets)
		userLogin.GET("/hosts/field/:id", hostFieldGet)
		userLogin.PUT("/hosts/field/:id", hostFieldPut)
		userLogin.DELETE("/hosts/field/:id", hostFieldDel)
		userLogin.GET("/host/:id/fields", hostFieldGets)
		userLogin.PUT("/host/:id/fields", hostFieldPuts)
		userLogin.POST("/hosts/fields/values", hostFieldValueGets)
		userLogin.POST("/host/filter", hostFilter)

		userLogin.GET("/hosts/csv/template", GetHostCSVTemplate)
		userLogin.POST("/hosts/csv/import", ImportHostsFromCSV)
		userLogin.GET("/hosts/csv/failed/:import_id", DownloadFailedData)
	}
}
