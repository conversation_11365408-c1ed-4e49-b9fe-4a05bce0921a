package sync

import (
	"fmt"

	"github.com/toolkits/pkg/logger"

	"arboris/src/models"
)

// ==================== RDB数据库查询方法 ====================

// getUsernamesByIDs 根据用户ID获取用户名列表
func (h *Handler) getUsernamesByIDs(userIDs []int64) ([]string, error) {
	if len(userIDs) == 0 {
		return []string{}, nil
	}

	// 使用XORM查询
	var users []models.User
	err := models.DB["rdb"].In("id", userIDs).Find(&users)
	if err != nil {
		return nil, fmt.Errorf("failed to query usernames: %v", err)
	}

	var usernames []string
	for _, user := range users {
		usernames = append(usernames, user.Username)
	}

	return usernames, nil
}

// getTeamNamesByIDs 根据团队ID获取团队名称列表
func (h *Handler) getTeamNamesByIDs(teamIDs []int64) ([]string, error) {
	if len(teamIDs) == 0 {
		return []string{}, nil
	}

	// 使用XORM查询
	var teams []models.Team
	err := models.DB["rdb"].In("id", teamIDs).Find(&teams)
	if err != nil {
		return nil, fmt.Errorf("failed to query team names: %v", err)
	}

	var teamNames []string
	for _, team := range teams {
		teamNames = append(teamNames, team.Ident)
	}

	return teamNames, nil
}

// getNodeByID 根据节点ID获取节点信息
func (h *Handler) getNodeByID(nodeID int64) (*NodeInfo, error) {
	// 使用XORM查询
	var node models.Node
	has, err := models.DB["rdb"].Where("id = ?", nodeID).Get(&node)
	if err != nil {
		return nil, fmt.Errorf("failed to query node: %v", err)
	}

	if !has {
		return nil, fmt.Errorf("node not found: %d", nodeID)
	}

	return &NodeInfo{
		ID:          node.Id,
		PID:         node.Pid,
		Ident:       node.Ident,
		Name:        node.Name,
		Note:        node.Note,
		Path:        node.Path,
		Leaf:        node.Leaf,
		Cate:        node.Cate,
		IconColor:   node.IconColor,
		IconChar:    node.IconChar,
		Proxy:       node.Proxy,
		Creator:     node.Creator,
		LastUpdated: node.LastUpdated.Unix(),
	}, nil
}

// getNodeByPath 根据节点路径获取节点信息
func (h *Handler) getNodeByPath(nodePath string) (*NodeInfo, error) {
	// 使用XORM查询
	var node models.Node
	has, err := models.DB["rdb"].Where("path = ?", nodePath).Get(&node)
	if err != nil {
		return nil, fmt.Errorf("failed to query node: %v", err)
	}

	if !has {
		return nil, fmt.Errorf("node not found: %s", nodePath)
	}

	return &NodeInfo{
		ID:   node.Id,
		Name: node.Name,
		Path: node.Path,
	}, nil
}

// ==================== AMS数据库查询方法 ====================

// getAssetsByNodePath 根据节点路径获取资产列表
func (h *Handler) getAssetsByNodePath(nodePath string) ([]string, error) {
	// 检查AMS数据库是否可用
	if models.DB["rdb"] == nil {
		logger.Infof("AMS database not connected, returning empty asset list for node: %s", nodePath)
		return []string{}, nil
	}

	// RDB的node path格式: inner.code.master
	// 查询该节点路径下的所有主机
	var hosts []models.Host

	// 支持路径匹配，包括子路径
	// 对于 inner.code.master，匹配 inner.code.master.% 的子节点
	pathPattern := nodePath + ".%"
	err := models.DB["rdb"].Where("node_path = ? OR node_path LIKE ?", nodePath, pathPattern).
		Cols("ident").Find(&hosts)
	if err != nil {
		return nil, fmt.Errorf("failed to query assets by node path: %v", err)
	}

	var assets []string
	for _, host := range hosts {
		assets = append(assets, host.Ident)
	}

	logger.Infof("Found %d assets for node path: %s", len(assets), nodePath)
	return assets, nil
}

// ==================== 数据结构定义 ====================

// NodeInfo 节点信息结构
type NodeInfo struct {
	ID          int64  `json:"id"`
	PID         int64  `json:"pid"`
	Ident       string `json:"ident"`
	Name        string `json:"name"`
	Note        string `json:"note"`
	Path        string `json:"path"`
	Leaf        int    `json:"leaf"`
	Cate        string `json:"cate"`
	IconColor   string `json:"icon_color"`
	IconChar    string `json:"icon_char"`
	Proxy       int    `json:"proxy"`
	Creator     string `json:"creator"`
	LastUpdated int64  `json:"last_updated"`
}

// HostInfo 主机信息结构
type HostInfo struct {
	ID       int64  `json:"id"`
	Ident    string `json:"ident"`
	Name     string `json:"name"`
	IP       string `json:"ip"`
	NodePath string `json:"node_path"`
}
