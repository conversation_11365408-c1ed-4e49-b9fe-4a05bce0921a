package sync

import (
	"encoding/json"
	"fmt"

	"github.com/toolkits/pkg/logger"

	"arboris/src/models"
	"arboris/src/modules/jumpserver-sync/config"
	"arboris/src/modules/jumpserver-sync/events"
	"arboris/src/modules/jumpserver-sync/jumpserver"
	"arboris/src/modules/jumpserver-sync/mapper"
)

// Handler 同步处理器
type Handler struct {
	jsClient *jumpserver.Client
	mapper   *mapper.Mapper
	config   *config.ConfigT

	// 缓存JumpServer节点和资产的映射关系
	nodeCache  map[string]*jumpserver.Node  // key: RDB path -> JumpServer Node
	assetCache map[string]*jumpserver.Asset // key: Host IP/UUID -> JumpServer Asset
}

// NewHandler 创建新的同步处理器
func NewHandler(cfg *config.ConfigT) (*Handler, error) {
	// 创建JumpServer客户端
	jsClient := jumpserver.NewClient(
		cfg.JumpServer.BaseURL,
		cfg.JumpServer.Username,
		cfg.JumpServer.Password,
		cfg.JumpServer.Token,
		cfg.JumpServer.Organization,
		cfg.JumpServer.Timeout,
	)

	// 测试连接
	if err := jsClient.TestConnection(); err != nil {
		return nil, fmt.Errorf("failed to connect to JumpServer: %v", err)
	}

	handler := &Handler{
		jsClient:   jsClient,
		mapper:     mapper.NewMapper(cfg),
		config:     cfg,
		nodeCache:  make(map[string]*jumpserver.Node),
		assetCache: make(map[string]*jumpserver.Asset),
	}

	// 初始化缓存
	if err := handler.initCache(); err != nil {
		logger.Infof("Failed to initialize cache: %v", err)
	}

	return handler, nil
}

// HandleEvent 处理事件
func (h *Handler) HandleEvent(event *events.Event) error {
	logger.Infof("Processing event: event_id=%s, event_type=%s, source=%s",
		event.ID, event.Type, event.Source)

	switch event.Type {
	case events.EventNodeCreate:
		return h.handleNodeCreate(event)
	case events.EventNodeUpdate:
		return h.handleNodeUpdate(event)
	case events.EventNodeDelete:
		return h.handleNodeDelete(event)
	case events.EventHostCreate:
		return h.handleHostCreate(event)
	case events.EventHostUpdate:
		return h.handleHostUpdate(event)
	case events.EventHostDelete:
		return h.handleHostDelete(event)
	case events.EventResourceCreate:
		return h.handleResourceCreate(event)
	case events.EventResourceUpdate:
		return h.handleResourceUpdate(event)
	case events.EventResourceDelete:
		return h.handleResourceDelete(event)
	case events.EventResourceBind:
		return h.handleResourceBind(event)
	case events.EventResourceUnbind:
		return h.handleResourceUnbind(event)
	// 用户管理事件
	case events.EventUserCreate:
		return h.handleUserCreate(event)
	case events.EventUserUpdate:
		return h.handleUserUpdate(event)
	case events.EventUserDelete:
		return h.handleUserDelete(event)
	case events.EventUserChangePassword:
		return h.handleUserChangePassword(event)
	case events.EventUserEnable:
		return h.handleUserEnable(event)
	case events.EventUserDisable:
		return h.handleUserDisable(event)
	// 用户组事件
	case events.EventTeamCreate:
		return h.handleTeamCreate(event)
	case events.EventTeamUpdate:
		return h.handleTeamUpdate(event)
	case events.EventTeamDelete:
		return h.handleTeamDelete(event)
	case events.EventTeamAddMember:
		return h.handleTeamAddUser(event)
	case events.EventTeamRemoveMember:
		return h.handleTeamRemoveUser(event)
	// 权限事件
	case events.EventPermissionGrant:
		return h.handlePermissionGrant(event)
	case events.EventPermissionRevoke:
		return h.handlePermissionRevoke(event)
	default:
		logger.Infof("Unknown event type: %s", event.Type)
		return nil
	}
}

// handleNodeCreate 处理节点创建事件
func (h *Handler) handleNodeCreate(event *events.Event) error {
	nodeData, err := h.parseNodeEventData(event)
	if err != nil {
		return err
	}

	// 检查是否应该同步
	if !h.mapper.ShouldSyncNode(nodeData) {
		logger.Infof("Node filtered out by sync rules: node_path=%s", nodeData.Path)
		return nil
	}

	// 映射为JumpServer节点
	jsNode, err := h.mapper.MapNodeToJumpServer(nodeData)
	if err != nil {
		return fmt.Errorf("failed to map node: %v", err)
	}

	// 检查节点是否已存在
	existingNode, err := h.jsClient.GetNodeByValue(jsNode.Value)
	if err != nil {
		return fmt.Errorf("failed to check existing node: %v", err)
	}

	if existingNode != nil {
		logger.Infof("Node already exists, skipping creation: node_key=%s", jsNode.Key)
		h.nodeCache[nodeData.Path] = existingNode
		return nil
	}

	// 确保父节点存在（如果不是根节点）
	if nodeData.GetNodePID() != 0 {
		parentNodeData, err := h.getNodeByID(nodeData.GetNodePID())
		if err != nil {
			return fmt.Errorf("failed to get parent node: %v", err)
		}

		// 递归确保父节点存在
		if _, exists := h.nodeCache[parentNodeData.Path]; !exists {
			parentEvent := &events.Event{
				Type: events.EventNodeCreate,
				Data: map[string]interface{}{
					"id":           parentNodeData.ID,
					"pid":          parentNodeData.PID,
					"ident":        parentNodeData.Ident,
					"name":         parentNodeData.Name,
					"note":         parentNodeData.Note,
					"path":         parentNodeData.Path,
					"leaf":         parentNodeData.Leaf,
					"cate":         parentNodeData.Cate,
					"icon_color":   parentNodeData.IconColor,
					"icon_char":    parentNodeData.IconChar,
					"proxy":        parentNodeData.Proxy,
					"creator":      parentNodeData.Creator,
					"last_updated": parentNodeData.LastUpdated,
				},
			}
			if err := h.handleNodeCreate(parentEvent); err != nil {
				return fmt.Errorf("failed to create parent node: %v", err)
			}
		}

		// 获取父节点的JumpServer ID
		parentJSNode := h.nodeCache[parentNodeData.Path]
		jsNode.Parent = parentJSNode.ID
	}

	// 创建节点
	createdNode, err := h.jsClient.CreateNode(jsNode)
	if err != nil {
		return fmt.Errorf("failed to create node in JumpServer: %v", err)
	}

	// 更新缓存
	h.nodeCache[nodeData.Path] = createdNode

	logger.Infof("Node created successfully: rdb_path=%s, js_key=%s, js_id=%s",
		nodeData.Path, createdNode.Key, createdNode.ID)

	return nil
}

// handleHostCreate 处理主机创建事件
func (h *Handler) handleHostCreate(event *events.Event) error {
	hostData, err := h.parseHostEventData(event)
	if err != nil {
		return err
	}

	// 检查是否应该同步
	if !h.mapper.ShouldSyncHost(hostData) {
		logger.Infof("Host filtered out by sync rules: host_ip=%s", hostData.IP)
		return nil
	}

	// 映射为JumpServer资产
	jsAsset, err := h.mapper.MapHostToJumpServer(hostData)
	if err != nil {
		return fmt.Errorf("failed to map host: %v", err)
	}

	// 检查资产是否已存在
	existingAsset, err := h.jsClient.GetAssetByIP(jsAsset.IP)
	if err != nil {
		return fmt.Errorf("failed to check existing asset: %v", err)
	}

	if existingAsset != nil {
		logger.Infof("Asset already exists, skipping creation: ip=%s", jsAsset.IP)
		h.assetCache[hostData.IP] = existingAsset
		return nil
	}

	// 创建资产
	createdAsset, err := h.jsClient.CreateAsset(jsAsset)
	if err != nil {
		return fmt.Errorf("failed to create asset in JumpServer: %v", err)
	}

	// 更新缓存
	h.assetCache[hostData.IP] = createdAsset

	logger.Infof("Asset created successfully: host_ip=%s, js_hostname=%s, js_id=%s",
		hostData.IP, createdAsset.Hostname, createdAsset.ID)

	return nil
}

// parseNodeEventData 解析节点事件数据
func (h *Handler) parseNodeEventData(event *events.Event) (*events.NodeEventData, error) {
	data, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event data: %v", err)
	}

	var nodeData events.NodeEventData
	if err := json.Unmarshal(data, &nodeData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal node event data: %v", err)
	}

	return &nodeData, nil
}

// parseHostEventData 解析主机事件数据
func (h *Handler) parseHostEventData(event *events.Event) (*events.HostEventData, error) {
	data, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event data: %v", err)
	}

	var hostData events.HostEventData
	if err := json.Unmarshal(data, &hostData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal host event data: %v", err)
	}

	return &hostData, nil
}

// parseResourceEventData 解析资源事件数据
func (h *Handler) parseResourceEventData(event *events.Event) (*events.ResourceEventData, error) {
	data, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event data: %v", err)
	}

	var resourceData events.ResourceEventData
	if err := json.Unmarshal(data, &resourceData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal resource event data: %v", err)
	}

	return &resourceData, nil
}

// parseResourceBindEventData 解析资源绑定事件数据
func (h *Handler) parseResourceBindEventData(event *events.Event) (*events.ResourceBindEventData, error) {
	data, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event data: %v", err)
	}

	var bindData events.ResourceBindEventData
	if err := json.Unmarshal(data, &bindData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal resource bind event data: %v", err)
	}

	return &bindData, nil
}

// handleNodeUpdate 处理节点更新事件
func (h *Handler) handleNodeUpdate(event *events.Event) error {
	nodeData, err := h.parseNodeEventData(event)
	if err != nil {
		return err
	}

	// 检查是否应该同步
	if !h.mapper.ShouldSyncNode(nodeData) {
		logger.Infof("Node filtered out by sync rules: node_path=%s", nodeData.Path)
		return nil
	}

	// 查找现有节点
	existingNode, exists := h.nodeCache[nodeData.Path]
	if !exists {
		// 如果有原始名称，尝试通过原始名称查找
		if nodeData.OriginalName != "" {
			logger.Infof("Searching for existing node by original name: %s", nodeData.OriginalName)
			existingNode, err = h.jsClient.GetNodeByValue(nodeData.OriginalName)
			if err != nil {
				logger.Errorf("Failed to find existing node by original name: %v", err)
				return fmt.Errorf("failed to find existing node by original name: %v", err)
			}
		}

		// 如果通过原始名称没找到，尝试用新名称查找
		if existingNode == nil {
			jsNode, err := h.mapper.MapNodeToJumpServer(nodeData)
			if err != nil {
				return fmt.Errorf("failed to map node: %v", err)
			}

			logger.Infof("Searching for existing node by new name: %s", jsNode.Value)
			existingNode, err = h.jsClient.GetNodeByValue(jsNode.Value)
			if err != nil {
				logger.Errorf("Failed to find existing node by new name: %v", err)
				return fmt.Errorf("failed to find existing node by new name: %v", err)
			}
		}

		if existingNode == nil {
			// 节点不存在，创建新节点
			logger.Infof("Node not found in JumpServer, creating new node: name=%s, original_name=%s", nodeData.Name, nodeData.OriginalName)
			return h.handleNodeCreate(event)
		}

		logger.Infof("Found existing node for update: js_id=%s, js_key=%s, js_value=%s", existingNode.ID, existingNode.Key, existingNode.Value)
		h.nodeCache[nodeData.Path] = existingNode
	}

	// 映射为JumpServer节点
	jsNode, err := h.mapper.MapNodeToJumpServer(nodeData)
	if err != nil {
		return fmt.Errorf("failed to map node: %v", err)
	}

	// 更新节点
	updatedNode, err := h.jsClient.UpdateNode(existingNode.ID, jsNode)
	if err != nil {
		return fmt.Errorf("failed to update node in JumpServer: %v", err)
	}

	// 更新缓存
	h.nodeCache[nodeData.Path] = updatedNode

	logger.Infof("Node updated successfully: rdb_path=%s, js_key=%s, js_id=%s",
		nodeData.Path, updatedNode.Key, updatedNode.ID)

	return nil
}

// handleNodeDelete 处理节点删除事件
func (h *Handler) handleNodeDelete(event *events.Event) error {
	nodeData, err := h.parseNodeEventData(event)
	if err != nil {
		return err
	}

	// 查找现有节点
	existingNode, exists := h.nodeCache[nodeData.Path]
	if !exists {
		logger.Infof("Node not found in cache, skipping deletion: node_path=%s", nodeData.Path)
		return nil
	}

	// 删除节点
	if err := h.jsClient.DeleteNode(existingNode.ID); err != nil {
		return fmt.Errorf("failed to delete node from JumpServer: %v", err)
	}

	// 从缓存中移除
	delete(h.nodeCache, nodeData.Path)

	logger.Infof("Node deleted successfully: rdb_path=%s, js_key=%s, js_id=%s",
		nodeData.Path, existingNode.Key, existingNode.ID)

	return nil
}

// handleHostUpdate 处理主机更新事件
func (h *Handler) handleHostUpdate(event *events.Event) error {
	hostData, err := h.parseHostEventData(event)
	if err != nil {
		return err
	}

	// 检查是否应该同步
	if !h.mapper.ShouldSyncHost(hostData) {
		logger.Infof("Host filtered out by sync rules: host_ip=%s", hostData.IP)
		return nil
	}

	// 查找现有资产
	existingAsset, exists := h.assetCache[hostData.IP]
	if !exists {
		// 尝试从JumpServer查找
		existingAsset, err = h.jsClient.GetAssetByIP(hostData.IP)
		if err != nil {
			return fmt.Errorf("failed to find existing asset: %v", err)
		}

		if existingAsset == nil {
			// 资产不存在，创建新资产
			return h.handleHostCreate(event)
		}

		h.assetCache[hostData.IP] = existingAsset
	}

	// 映射为JumpServer资产
	jsAsset, err := h.mapper.MapHostToJumpServer(hostData)
	if err != nil {
		return fmt.Errorf("failed to map host: %v", err)
	}

	// 更新资产
	updatedAsset, err := h.jsClient.UpdateAsset(existingAsset.ID, jsAsset)
	if err != nil {
		return fmt.Errorf("failed to update asset in JumpServer: %v", err)
	}

	// 更新缓存
	h.assetCache[hostData.IP] = updatedAsset

	logger.Infof("Asset updated successfully: host_ip=%s, js_hostname=%s, js_id=%s",
		hostData.IP, updatedAsset.Hostname, updatedAsset.ID)

	return nil
}

// handleHostDelete 处理主机删除事件
func (h *Handler) handleHostDelete(event *events.Event) error {
	hostData, err := h.parseHostEventData(event)
	if err != nil {
		return err
	}

	// 查找现有资产
	existingAsset, exists := h.assetCache[hostData.IP]
	if !exists {
		logger.Infof("Asset not found in cache, skipping deletion: host_ip=%s", hostData.IP)
		return nil
	}

	// 删除资产
	if err := h.jsClient.DeleteAsset(existingAsset.ID); err != nil {
		return fmt.Errorf("failed to delete asset from JumpServer: %v", err)
	}

	// 从缓存中移除
	delete(h.assetCache, hostData.IP)

	logger.Infof("Asset deleted successfully: host_ip=%s, js_hostname=%s, js_id=%s",
		hostData.IP, existingAsset.Hostname, existingAsset.ID)

	return nil
}

// handleResourceCreate, handleResourceUpdate, handleResourceDelete 处理资源事件
// 这些方法的实现类似于主机事件处理，但处理的是RDB中的resource表数据

func (h *Handler) handleResourceCreate(event *events.Event) error {
	// 实现资源创建逻辑
	logger.Infof("Resource create event received: %s", event.ID)
	return nil
}

func (h *Handler) handleResourceUpdate(event *events.Event) error {
	// 实现资源更新逻辑
	logger.Infof("Resource update event received: %s", event.ID)
	return nil
}

func (h *Handler) handleResourceDelete(event *events.Event) error {
	// 实现资源删除逻辑
	logger.Infof("Resource delete event received: %s", event.ID)
	return nil
}

func (h *Handler) handleResourceBind(event *events.Event) error {
	bindData, err := h.parseResourceBindEventData(event)
	if err != nil {
		return err
	}

	logger.Infof("Resource bind event: node_path=%s, resource_uuid=%s",
		bindData.NodePath, bindData.ResourceUUID)

	// 获取RDB中的资源信息
	resource, err := h.getResourceByUUID(bindData.ResourceUUID)
	if err != nil {
		return fmt.Errorf("failed to get resource by UUID %s: %v", bindData.ResourceUUID, err)
	}

	if resource == nil {
		logger.Infof("Resource not found in RDB: uuid=%s", bindData.ResourceUUID)
		return nil
	}

	// 检查资源类型，只处理host类型的资源
	if resource.SourceType != "physical" {
		logger.Infof("Resource is not a physical, skipping: uuid=%s, source_type=%s",
			resource.UUID, resource.SourceType)
		return nil
	}

	// 根据source_id从AMS库获取主机信息
	host, err := h.getHostBySourceID(resource.SourceId)
	if err != nil {
		return fmt.Errorf("failed to get host by source_id %d: %v", resource.SourceId, err)
	}

	if host == nil {
		logger.Infof("Host not found in AMS: source_id=%d", resource.SourceId)
		return nil
	}

	// 获取JumpServer中对应的节点
	nodePK, _ := h.convertNodePathToJSNodePK(bindData.NodePath)
	if nodePK == "" {
		return fmt.Errorf("failed to find JumpServer node for path: %s", bindData.NodePath)
	}

	// 检查JumpServer中是否已存在该资产
	var asset *jumpserver.Asset

	// 首先尝试通过主机IP查找
	if host.IP != "" {
		asset, err = h.jsClient.GetAssetByIP(host.IP)
		if err != nil {
			logger.Errorf("Failed to get asset by IP %s: %v", host.IP, err)
		}
	}

	// 如果通过IP没找到，尝试通过主机名查找
	if asset == nil && host.Name != "" {
		asset, err = h.jsClient.GetAssetByName(host.Name)
		if err != nil {
			logger.Errorf("Failed to get asset by name %s: %v", host.Name, err)
		}
	}

	// 如果通过主机名没找到，尝试通过标识查找
	if asset == nil && host.Ident != "" {
		asset, err = h.jsClient.GetAssetByName(host.Ident)
		if err != nil {
			logger.Errorf("Failed to get asset by ident %s: %v", host.Ident, err)
		}
	}

	// 如果资产不存在，创建新资产
	if asset == nil {
		logger.Infof("Asset not found in JumpServer, creating new asset: name=%s, ip=%s, ident=%s",
			host.Name, host.IP, host.Ident)

		newAsset := &jumpserver.Asset{
			Hostname: host.Name,
			Name:     host.Name,
			IP:       host.IP,
			Address:  host.IP,
			Comment:  fmt.Sprintf("Auto-synced from AMS - Host ID: %d, Resource UUID: %s", host.Id, resource.UUID),
			Nodes:    []string{nodePK}, // 直接绑定到目标节点
			IsActive: true,
		}

		asset, err = h.jsClient.CreateAsset(newAsset)
		if err != nil {
			return fmt.Errorf("failed to create asset in JumpServer: %v", err)
		}

		logger.Infof("Asset created in JumpServer: id=%s, name=%s, ip=%s, node=%s",
			asset.ID, asset.Hostname, asset.IP, nodePK)
	} else {
		// 资产已存在，更新其节点绑定
		logger.Infof("Asset found in JumpServer: id=%s, name=%s, hostname=%s, address=%s, ip=%s",
			asset.ID, asset.Name, asset.Hostname, asset.Address, asset.IP)

		// 检查资产是否已经绑定到该节点
		nodeAlreadyBound := false
		for _, nodeID := range asset.Nodes {
			if nodeID == nodePK {
				nodeAlreadyBound = true
				break
			}
		}

		if !nodeAlreadyBound {
			// 添加节点到资产的节点列表
			asset.Nodes = append(asset.Nodes, nodePK)

			// 更新资产
			_, err = h.jsClient.UpdateAsset(asset.ID, asset)
			if err != nil {
				return fmt.Errorf("failed to update asset nodes in JumpServer: %v", err)
			}

			logger.Infof("Asset updated with new node binding: asset_id=%s, node=%s",
				asset.ID, nodePK)
		} else {
			logger.Infof("Asset already bound to node: asset_id=%s, node=%s",
				asset.ID, nodePK)
		}
	}

	return nil
}

func (h *Handler) handleResourceUnbind(event *events.Event) error {
	bindData, err := h.parseResourceBindEventData(event)
	if err != nil {
		return err
	}

	logger.Infof("Resource unbind event: node_path=%s, resource_uuid=%s",
		bindData.NodePath, bindData.ResourceUUID)

	// 获取RDB中的资源信息
	resource, err := h.getResourceByUUID(bindData.ResourceUUID)
	if err != nil {
		return fmt.Errorf("failed to get resource by UUID %s: %v", bindData.ResourceUUID, err)
	}

	if resource == nil {
		logger.Infof("Resource not found in RDB: uuid=%s", bindData.ResourceUUID)
		return nil
	}

	// 检查资源类型，只处理host类型的资源
	if resource.SourceType != "physical" {
		logger.Infof("Resource is not a physical, skipping: uuid=%s, source_type=%s",
			resource.UUID, resource.SourceType)
		return nil
	}

	// 根据source_id从AMS库获取主机信息
	host, err := h.getHostBySourceID(resource.SourceId)
	if err != nil {
		return fmt.Errorf("failed to get host by source_id %d: %v", resource.SourceId, err)
	}

	if host == nil {
		logger.Infof("Host not found in AMS: source_id=%d", resource.SourceId)
		return nil
	}

	// 获取JumpServer中对应的节点
	nodePK, _ := h.convertNodePathToJSNodePK(bindData.NodePath)
	if nodePK == "" {
		return fmt.Errorf("failed to find JumpServer node for path: %s", bindData.NodePath)
	}

	// 查找JumpServer中对应的资产
	var asset *jumpserver.Asset

	// 首先尝试通过主机IP查找
	if host.IP != "" {
		asset, err = h.jsClient.GetAssetByIP(host.IP)
		if err != nil {
			logger.Errorf("Failed to get asset by IP %s: %v", host.IP, err)
		}
	}

	// 如果通过IP没找到，尝试通过主机名查找
	if asset == nil && host.Name != "" {
		asset, err = h.jsClient.GetAssetByName(host.Name)
		if err != nil {
			logger.Errorf("Failed to get asset by name %s: %v", host.Name, err)
		}
	}

	// 如果通过主机名没找到，尝试通过标识查找
	if asset == nil && host.Ident != "" {
		asset, err = h.jsClient.GetAssetByName(host.Ident)
		if err != nil {
			logger.Errorf("Failed to get asset by ident %s: %v", host.Ident, err)
		}
	}

	if asset == nil {
		logger.Infof("Asset not found in JumpServer: name=%s, ip=%s, ident=%s",
			host.Name, host.IP, host.Ident)
		return nil
	}

	logger.Infof("Asset found in JumpServer: id=%s, name=%s, hostname=%s, address=%s, ip=%s",
		asset.ID, asset.Name, asset.Hostname, asset.Address, asset.IP)

	// 从资产的节点列表中移除指定节点
	var updatedNodes []string
	nodeRemoved := false
	for _, nodeID := range asset.Nodes {
		if nodeID != nodePK {
			updatedNodes = append(updatedNodes, nodeID)
		} else {
			nodeRemoved = true
		}
	}

	if !nodeRemoved {
		logger.Infof("Asset was not bound to node: asset_id=%s, node=%s",
			asset.ID, nodePK)
		return nil
	}

	// 如果资产没有绑定到任何节点了，删除资产
	if len(updatedNodes) == 0 {
		logger.Infof("Asset has no more node bindings, deleting asset: asset_id=%s", asset.ID)

		err = h.jsClient.DeleteAsset(asset.ID)
		if err != nil {
			return fmt.Errorf("failed to delete asset from JumpServer: %v", err)
		}

		logger.Infof("Asset deleted from JumpServer: asset_id=%s", asset.ID)
	} else {
		// 更新资产的节点绑定
		asset.Nodes = updatedNodes

		_, err = h.jsClient.UpdateAsset(asset.ID, asset)
		if err != nil {
			return fmt.Errorf("failed to update asset nodes in JumpServer: %v", err)
		}

		logger.Infof("Asset updated, node binding removed: asset_id=%s, removed_node=%s, remaining_nodes=%v",
			asset.ID, nodePK, updatedNodes)
	}

	return nil
}

// ==================== 用户管理事件处理 ====================

// handleUserCreate 处理用户创建事件
func (h *Handler) handleUserCreate(event *events.Event) error {
	userData, err := h.parseUserEventData(event)
	if err != nil {
		return err
	}

	logger.Infof("Creating user: username=%s, dispname=%s", userData.Username, userData.Dispname)

	// 映射RDB用户到JumpServer用户
	jsUser := &jumpserver.User{
		Username:    userData.Username,
		Name:        userData.Dispname,
		Email:       userData.Email,
		Phone:       userData.Phone,
		Password:    userData.Password,         // 包含密码，由CreateUserWithPassword方法处理
		IsActive:    userData.GetStatus() == 0, // RDB中0表示active
		IsSuperuser: userData.GetIsRoot() == 1,
		Comment:     fmt.Sprintf("Synced from RDB - Org: %s", userData.Organization),
		Source:      "local",         // 使用"local"而不是"rdb"
		Groups:      []interface{}{}, // 初始化为空数组
		SystemRoles: []interface{}{}, // 初始化为空数组
		OrgRoles:    []interface{}{}, // 初始化为空数组
	}

	// 使用两步操作创建用户并设置密码
	createdUser, err := h.jsClient.CreateUserWithPassword(jsUser)
	if err != nil {
		return fmt.Errorf("failed to create user in JumpServer: %v", err)
	}

	logger.Infof("User creation completed: username=%s, js_id=%s", userData.Username, createdUser.ID)
	return nil
}

// handleUserUpdate 处理用户更新事件
func (h *Handler) handleUserUpdate(event *events.Event) error {
	userData, err := h.parseUserEventData(event)
	if err != nil {
		return err
	}

	logger.Infof("Updating user: username=%s", userData.Username)

	// 获取现有用户
	existingUser, err := h.jsClient.GetUser(userData.Username)
	if err != nil {
		logger.Infof("User not found in JumpServer, creating new user: %s", userData.Username)
		return h.handleUserCreate(event)
	}

	// 更新用户信息
	existingUser.Name = userData.Dispname
	existingUser.Email = userData.Email
	existingUser.Phone = userData.Phone
	existingUser.IsActive = userData.GetStatus() == 0
	existingUser.IsSuperuser = userData.GetIsRoot() == 1
	existingUser.Comment = fmt.Sprintf("Synced from RDB - Org: %s", userData.Organization)

	// 执行更新
	_, err = h.jsClient.UpdateUser(existingUser.ID, existingUser)
	if err != nil {
		return fmt.Errorf("failed to update user in JumpServer: %v", err)
	}

	logger.Infof("User updated successfully: username=%s, js_id=%s", userData.Username, existingUser.ID)
	return nil
}

// handleUserDelete 处理用户删除事件
func (h *Handler) handleUserDelete(event *events.Event) error {
	userData, err := h.parseUserEventData(event)
	if err != nil {
		return err
	}

	logger.Infof("Deleting user: username=%s", userData.Username)

	// 获取现有用户
	existingUser, err := h.jsClient.GetUser(userData.Username)
	if err != nil {
		logger.Infof("User not found in JumpServer, skipping deletion: %s", userData.Username)
		return nil
	}

	// 直接从JumpServer删除用户
	err = h.jsClient.DeleteUser(existingUser.ID)
	if err != nil {
		return fmt.Errorf("failed to delete user from JumpServer: %v", err)
	}

	logger.Infof("User deleted successfully: username=%s, js_id=%s", userData.Username, existingUser.ID)
	return nil
}

// handleUserEnable 处理用户启用事件
func (h *Handler) handleUserEnable(event *events.Event) error {
	userData, err := h.parseUserEventData(event)
	if err != nil {
		return err
	}

	return h.updateUserStatus(userData.Username, true)
}

// handleUserDisable 处理用户禁用事件
func (h *Handler) handleUserDisable(event *events.Event) error {
	userData, err := h.parseUserEventData(event)
	if err != nil {
		return err
	}

	return h.updateUserStatus(userData.Username, false)
}

// updateUserStatus 更新用户状态的辅助方法
func (h *Handler) updateUserStatus(username string, isActive bool) error {
	logger.Infof("Updating user status: username=%s, active=%v", username, isActive)

	// 获取现有用户
	existingUser, err := h.jsClient.GetUser(username)
	if err != nil {
		return fmt.Errorf("user not found in JumpServer: %s", username)
	}

	// 使用PATCH方法只更新状态字段
	err = h.jsClient.UpdateUserStatus(existingUser.ID, isActive)
	if err != nil {
		return fmt.Errorf("failed to update user status in JumpServer: %v", err)
	}

	logger.Infof("User status updated successfully: username=%s, active=%v, js_id=%s", username, isActive, existingUser.ID)
	return nil
}

// handleUserChangePassword 处理用户密码修改事件
func (h *Handler) handleUserChangePassword(event *events.Event) error {
	passwordData, err := h.parsePasswordChangeEventData(event)
	if err != nil {
		return err
	}

	logger.Infof("Changing password for user: username=%s", passwordData.Username)

	// 获取现有用户
	existingUser, err := h.jsClient.GetUser(passwordData.Username)
	if err != nil {
		return fmt.Errorf("user not found in JumpServer: %s", passwordData.Username)
	}

	// 修改密码
	err = h.jsClient.ChangeUserPassword(existingUser.ID, passwordData.NewPassword)
	if err != nil {
		return fmt.Errorf("failed to change user password in JumpServer: %v", err)
	}

	logger.Infof("Password changed successfully: username=%s, js_id=%s", passwordData.Username, existingUser.ID)
	return nil
}

// ==================== 事件数据解析辅助方法 ====================

// parseUserEventData 解析用户事件数据
func (h *Handler) parseUserEventData(event *events.Event) (*events.UserEventData, error) {
	dataBytes, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event data: %v", err)
	}

	var userData events.UserEventData
	err = json.Unmarshal(dataBytes, &userData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal user event data: %v", err)
	}

	return &userData, nil
}

// parsePasswordChangeEventData 解析密码变更事件数据
func (h *Handler) parsePasswordChangeEventData(event *events.Event) (*events.PasswordChangeEventData, error) {
	dataBytes, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event data: %v", err)
	}

	var passwordData events.PasswordChangeEventData
	err = json.Unmarshal(dataBytes, &passwordData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal password change event data: %v", err)
	}

	return &passwordData, nil
}

// parseTeamEventData 解析团队事件数据
func (h *Handler) parseTeamEventData(event *events.Event) (*events.TeamEventData, error) {
	dataBytes, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event data: %v", err)
	}

	var teamData events.TeamEventData
	err = json.Unmarshal(dataBytes, &teamData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal team event data: %v", err)
	}

	return &teamData, nil
}

// parsePermissionEventData 解析权限事件数据
func (h *Handler) parsePermissionEventData(event *events.Event) (*events.PermissionEventData, error) {
	dataBytes, err := json.Marshal(event.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event data: %v", err)
	}

	var permissionData events.PermissionEventData
	err = json.Unmarshal(dataBytes, &permissionData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal permission event data: %v", err)
	}

	return &permissionData, nil
}

// initCache 初始化缓存
func (h *Handler) initCache() error {
	// 这里可以从JumpServer加载现有的节点和资产信息到缓存
	// 为了简化，暂时跳过初始化缓存的实现
	logger.Info("Cache initialization skipped")
	return nil
}

// ==================== 资源管理辅助方法 ====================

// getResourceByUUID 根据UUID获取资源信息
func (h *Handler) getResourceByUUID(uuid string) (*models.Resource, error) {
	// 检查RDB数据库是否可用
	if models.DB["rdb"] == nil {
		return nil, fmt.Errorf("RDB database not connected")
	}

	// 查询资源信息
	var resource models.Resource
	has, err := models.DB["rdb"].Where("uuid = ?", uuid).Get(&resource)
	if err != nil {
		return nil, fmt.Errorf("failed to query resource by UUID %s: %v", uuid, err)
	}

	if !has {
		return nil, nil // 资源不存在
	}

	logger.Infof("Found RDB resource: uuid=%s, name=%s, ident=%s, source_id=%d, source_type=%s",
		resource.UUID, resource.Name, resource.Ident, resource.SourceId, resource.SourceType)
	return &resource, nil
}

// getHostBySourceID 根据source_id从AMS库获取主机信息
func (h *Handler) getHostBySourceID(sourceID int64) (*models.Host, error) {
	// 检查AMS数据库是否可用
	if models.DB["ams"] == nil {
		return nil, fmt.Errorf("AMS database not connected")
	}

	// 查询主机信息
	var host models.Host
	has, err := models.DB["ams"].Where("id = ?", sourceID).Get(&host)
	if err != nil {
		return nil, fmt.Errorf("failed to query host by ID %d: %v", sourceID, err)
	}

	if !has {
		return nil, nil // 主机不存在
	}

	logger.Infof("Found AMS host: id=%d, name=%s, ip=%s, ident=%s",
		host.Id, host.Name, host.IP, host.Ident)
	return &host, nil
}
