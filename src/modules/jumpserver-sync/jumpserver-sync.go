package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	_ "github.com/go-sql-driver/mysql"
	"github.com/toolkits/pkg/logger"

	"arboris/src/common/loggeri"
	"arboris/src/models"
	"arboris/src/modules/jumpserver-sync/config"
	"arboris/src/modules/jumpserver-sync/events"
	"arboris/src/modules/jumpserver-sync/http"
	"arboris/src/modules/jumpserver-sync/sync"
)

var (
	vers *bool
	help *bool
	conf *string

	version = "1.0.0"
)

func init() {
	vers = flag.Bool("v", false, "display the version.")
	help = flag.Bool("h", false, "print this help.")
	conf = flag.String("f", "", "specify configuration file.")
	flag.Parse()

	if *vers {
		fmt.Println("Version:", version)
		os.Exit(0)
	}

	if *help {
		flag.Usage()
		os.Exit(0)
	}
}

func main() {
	parseConf()

	// 初始化logger（与RDB风格保持一致）
	loggerConfig := loggeri.Config{
		Dir:       config.Config.Logger.Dir,
		Level:     config.Config.Logger.Level,
		KeepHours: config.Config.Logger.KeepHours,
	}
	loggeri.Init(loggerConfig)

	logger.Info("Starting JumpServer sync service")

	// 初始化数据库连接（使用RDB风格）
	models.InitMySQL("rdb", "ams")

	// 创建同步处理器
	handler, err := sync.NewHandler(config.Config)
	if err != nil {
		logger.Errorf("Failed to create sync handler: %v", err)
		os.Exit(1)
	}

	// 创建Redis Streams消费者
	consumer, err := events.NewConsumer(
		config.Config.Redis.Addr,
		config.Config.Redis.Password,
		config.Config.Redis.DB,
		config.Config.Sync.Consumer.Stream,
		config.Config.Sync.Consumer.Group,
		config.Config.Sync.Consumer.Consumer,
		int64(config.Config.Sync.Consumer.BatchSize),
		config.Config.Sync.Consumer.PollInterval,
		handler,
	)
	if err != nil {
		logger.Errorf("Failed to create event consumer: %v", err)
		os.Exit(1)
	}

	// 启动HTTP服务器（用于健康检查和监控）
	http.Start()

	// 启动消费者
	consumer.Start()
	logger.Info("Event consumer started")

	// 等待退出信号
	waitForShutdown(consumer)
}

// parseConf 解析配置文件（与RDB风格保持一致）
func parseConf() {
	if err := config.Parse(); err != nil {
		fmt.Printf("cannot parse configuration file: %v\n", err)
		os.Exit(1)
	}
}

// waitForShutdown 等待关闭信号
func waitForShutdown(consumer *events.Consumer) {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)

	sig := <-sigChan
	logger.Infof("Received shutdown signal: %s", sig.String())

	// 关闭消费者
	logger.Info("Stopping event consumer")
	consumer.Stop()

	// 关闭HTTP服务器
	logger.Info("Stopping HTTP server")
	http.Shutdown()

	// 关闭logger
	logger.Close()

	logger.Info("JumpServer sync service stopped")
}
