package test

import (
	"testing"
	"time"

	"github.com/toolkits/pkg/logger"

	"arboris/src/modules/jumpserver-sync/jumpserver"
)

// TestGetAssetPermissionWithDetail 测试获取权限详情的功能
func TestGetAssetPermissionWithDetail(t *testing.T) {
	// 初始化日志
	logger.SetLevel("info")

	// 创建JumpServer客户端
	client := jumpserver.NewClient(
		"http://**********:8090",
		"",
		"",
		"your-token-here", // 需要替换为实际的token
		"********-0000-0000-0000-************",
		30*time.Second,
	)

	// 测试连接
	err := client.TestConnection()
	if err != nil {
		t.Skipf("JumpServer connection failed, skipping test: %v", err)
		return
	}

	// 创建测试权限
	testPermission := &jumpserver.AssetPermission{
		Name:        "test-permission-detail-001",
		Users:       []map[string]string{},
		UserGroups:  []map[string]string{},
		Assets:      []map[string]string{},
		Nodes:       []map[string]string{},
		Accounts:    []string{"@ALL"},
		Protocols:   []string{"all"},
		Actions:     []map[string]interface{}{{"value": "connect", "label": "连接 (所有协议)"}},
		IsActive:    true,
		DateStart:   time.Now().Format("2006-01-02T15:04:05Z"),
		DateExpired: "2099-12-31T23:59:59Z",
		Comment:     "测试权限详情获取 - 自动化测试创建",
	}

	// 创建权限
	createdPermission, err := client.CreateAssetPermission(testPermission)
	if err != nil {
		t.Fatalf("Failed to create asset permission: %v", err)
	}
	logger.Infof("Created asset permission: id=%s, name=%s", createdPermission.ID, createdPermission.Name)

	// 确保测试结束时清理权限
	defer func() {
		err := client.DeleteAssetPermission(createdPermission.ID)
		if err != nil {
			logger.Errorf("Failed to cleanup test permission: %v", err)
		} else {
			logger.Infof("Cleaned up test permission: %s", createdPermission.ID)
		}
	}()

	// 测试通过名称获取权限（这会触发我们的新逻辑）
	foundPermission, err := client.GetAssetPermission("test-permission-detail-001")
	if err != nil {
		t.Fatalf("Failed to get asset permission: %v", err)
	}

	// 验证返回的权限包含完整信息
	if foundPermission.ID != createdPermission.ID {
		t.Errorf("Expected permission ID %s, got %s", createdPermission.ID, foundPermission.ID)
	}

	if foundPermission.Name != "test-permission-detail-001" {
		t.Errorf("Expected permission name 'test-permission-detail-001', got %s", foundPermission.Name)
	}

	// 验证详细信息字段存在（这些字段在搜索接口中可能不完整）
	if foundPermission.Users == nil {
		t.Error("Expected Users field to be initialized, got nil")
	}

	if foundPermission.UserGroups == nil {
		t.Error("Expected UserGroups field to be initialized, got nil")
	}

	if foundPermission.Assets == nil {
		t.Error("Expected Assets field to be initialized, got nil")
	}

	if foundPermission.Nodes == nil {
		t.Error("Expected Nodes field to be initialized, got nil")
	}

	if len(foundPermission.Accounts) == 0 {
		t.Error("Expected Accounts field to have values")
	}

	if len(foundPermission.Protocols) == 0 {
		t.Error("Expected Protocols field to have values")
	}

	if len(foundPermission.Actions) == 0 {
		t.Error("Expected Actions field to have values")
	}

	logger.Infof("Permission detail test passed: id=%s, name=%s", foundPermission.ID, foundPermission.Name)
	logger.Infof("Permission details - Users: %+v, UserGroups: %+v, Assets: %+v, Nodes: %+v", 
		foundPermission.Users, foundPermission.UserGroups, foundPermission.Assets, foundPermission.Nodes)

	// 测试直接调用详情接口
	detailPermission, err := client.GetAssetPermissionDetail(createdPermission.ID)
	if err != nil {
		t.Fatalf("Failed to get asset permission detail: %v", err)
	}

	if detailPermission.ID != createdPermission.ID {
		t.Errorf("Expected detail permission ID %s, got %s", createdPermission.ID, detailPermission.ID)
	}

	logger.Infof("Direct permission detail test passed: id=%s, name=%s", detailPermission.ID, detailPermission.Name)
}
