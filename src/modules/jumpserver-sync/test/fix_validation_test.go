package test

import (
	"fmt"
	"strings"
	"testing"

	"arboris/src/modules/jumpserver-sync/events"
	"arboris/src/modules/jumpserver-sync/jumpserver"
	"arboris/src/modules/jumpserver-sync/mapper"
	"arboris/src/modules/jumpserver-sync/config"
)

// TestSameNameNodeMappingFix 测试同名节点映射修复
func TestSameNameNodeMappingFix(t *testing.T) {
	// 模拟配置
	cfg := &config.ConfigT{
		Sync: config.SyncConfig{
			Mapping: config.MappingConfig{
				NodeRules: []config.NodeRule{},
			},
		},
	}

	mapper := mapper.NewMapper(cfg)

	// 测试用例：不同层级的同名节点
	testCases := []struct {
		name        string
		nodeData    *events.NodeEventData
		expectedKey string
		description string
	}{
		{
			name: "部门A下的开发组",
			nodeData: &events.NodeEventData{
				ID:   1,
				Name: "开发组",
				Path: "公司.部门A.开发组",
				Cate: "department",
				Note: "部门A的开发组",
			},
			expectedKey: "/公司/部门A/开发组",
			description: "第一个开发组节点",
		},
		{
			name: "部门B下的开发组",
			nodeData: &events.NodeEventData{
				ID:   2,
				Name: "开发组",
				Path: "公司.部门B.开发组",
				Cate: "department",
				Note: "部门B的开发组",
			},
			expectedKey: "/公司/部门B/开发组",
			description: "第二个开发组节点（同名但不同路径）",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 测试节点映射
			jsNode, err := mapper.MapNodeToJumpServer(tc.nodeData)
			if err != nil {
				t.Errorf("节点映射失败: %v", err)
				return
			}

			// 验证映射结果
			if jsNode.FullPath != tc.expectedKey {
				t.Errorf("路径映射错误: 期望=%s, 实际=%s", tc.expectedKey, jsNode.FullPath)
			}

			if jsNode.Value != tc.nodeData.Name {
				t.Errorf("节点名称映射错误: 期望=%s, 实际=%s", tc.nodeData.Name, jsNode.Value)
			}

			// 验证元数据
			if jsNode.Meta["rdb_path"] != tc.nodeData.Path {
				t.Errorf("RDB路径元数据错误: 期望=%s, 实际=%s", tc.nodeData.Path, jsNode.Meta["rdb_path"])
			}

			t.Logf("✅ %s: 路径=%s, 名称=%s", tc.description, jsNode.FullPath, jsNode.Value)
		})
	}
}

// MockJumpServerClient 模拟JumpServer客户端，用于测试
type MockJumpServerClient struct {
	nodes map[string]*jumpserver.Node // key: FullPath, value: Node
}

func NewMockJumpServerClient() *MockJumpServerClient {
	return &MockJumpServerClient{
		nodes: make(map[string]*jumpserver.Node),
	}
}

func (m *MockJumpServerClient) GetNodeByPath(path string) (*jumpserver.Node, error) {
	if node, exists := m.nodes[path]; exists {
		return node, nil
	}
	return nil, nil // 节点不存在
}

func (m *MockJumpServerClient) GetNodeByValue(value string) (*jumpserver.Node, error) {
	// 模拟原有的按名称查找逻辑（有问题的逻辑）
	for _, node := range m.nodes {
		if node.Value == value {
			return node, nil // 返回第一个匹配的节点，这就是问题所在
		}
	}
	return nil, nil
}

func (m *MockJumpServerClient) CreateNode(node *jumpserver.Node) (*jumpserver.Node, error) {
	// 生成ID
	node.ID = fmt.Sprintf("node-%d", len(m.nodes)+1)
	
	// 存储节点
	m.nodes[node.FullPath] = node
	
	return node, nil
}

// TestSameNameNodeCreationFix 测试同名节点创建的修复效果
func TestSameNameNodeCreationFix(t *testing.T) {
	client := NewMockJumpServerClient()
	
	// 创建第一个"开发组"节点
	node1 := &jumpserver.Node{
		Key:      "开发组",
		Value:    "开发组", 
		FullPath: "/公司/部门A/开发组",
		Parent:   "dept-a",
	}
	
	createdNode1, err := client.CreateNode(node1)
	if err != nil {
		t.Errorf("创建第一个节点失败: %v", err)
		return
	}
	t.Logf("✅ 创建第一个开发组节点: ID=%s, 路径=%s", createdNode1.ID, createdNode1.FullPath)
	
	// 使用修复后的逻辑检查节点是否存在（按路径查找）
	existing1, err := client.GetNodeByPath("/公司/部门A/开发组")
	if err != nil {
		t.Errorf("按路径查找节点失败: %v", err)
		return
	}
	if existing1 == nil {
		t.Errorf("按路径查找节点失败: 节点不存在")
		return
	}
	t.Logf("✅ 按路径找到第一个节点: ID=%s", existing1.ID)
	
	// 创建第二个"开发组"节点（同名但不同路径）
	node2 := &jumpserver.Node{
		Key:      "开发组",
		Value:    "开发组",
		FullPath: "/公司/部门B/开发组", 
		Parent:   "dept-b",
	}
	
	// 使用修复后的逻辑检查第二个节点是否存在（按路径查找）
	existing2, err := client.GetNodeByPath("/公司/部门B/开发组")
	if err != nil {
		t.Errorf("按路径查找第二个节点失败: %v", err)
		return
	}
	if existing2 != nil {
		t.Errorf("第二个节点不应该存在，但找到了: ID=%s", existing2.ID)
		return
	}
	t.Logf("✅ 第二个节点路径不存在，可以创建")
	
	// 创建第二个节点
	createdNode2, err := client.CreateNode(node2)
	if err != nil {
		t.Errorf("创建第二个节点失败: %v", err)
		return
	}
	t.Logf("✅ 创建第二个开发组节点: ID=%s, 路径=%s", createdNode2.ID, createdNode2.FullPath)
	
	// 验证两个节点都存在且ID不同
	if createdNode1.ID == createdNode2.ID {
		t.Errorf("两个节点的ID相同，这是错误的: ID=%s", createdNode1.ID)
		return
	}
	
	// 验证按名称查找的问题（旧逻辑的问题演示）
	nodeByName, err := client.GetNodeByValue("开发组")
	if err != nil {
		t.Errorf("按名称查找节点失败: %v", err)
		return
	}
	if nodeByName != nil {
		t.Logf("⚠️  按名称查找只返回第一个匹配的节点: ID=%s, 路径=%s", 
			nodeByName.ID, nodeByName.FullPath)
		t.Logf("⚠️  这就是为什么旧逻辑会跳过同名节点的创建")
	}
	
	t.Logf("✅ 修复验证完成: 两个同名节点都成功创建，路径分别为:")
	t.Logf("   - 节点1: ID=%s, 路径=%s", createdNode1.ID, createdNode1.FullPath)
	t.Logf("   - 节点2: ID=%s, 路径=%s", createdNode2.ID, createdNode2.FullPath)
}

// TestPathConversionLogic 测试路径转换逻辑
func TestPathConversionLogic(t *testing.T) {
	testCases := []struct {
		rdbPath      string
		expectedPath string
		description  string
	}{
		{
			rdbPath:      "公司.部门A.开发组",
			expectedPath: "/公司/部门A/开发组",
			description:  "三级路径转换",
		},
		{
			rdbPath:      "inner.code.master",
			expectedPath: "/inner/code/master",
			description:  "英文路径转换",
		},
		{
			rdbPath:      "根节点",
			expectedPath: "/根节点",
			description:  "单级路径转换",
		},
		{
			rdbPath:      "",
			expectedPath: "/",
			description:  "空路径转换",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.description, func(t *testing.T) {
			converted := convertPath(tc.rdbPath)
			if converted != tc.expectedPath {
				t.Errorf("路径转换失败: 输入=%s, 期望=%s, 实际=%s", 
					tc.rdbPath, tc.expectedPath, converted)
			} else {
				t.Logf("✅ 路径转换成功: %s -> %s", tc.rdbPath, converted)
			}
		})
	}
}

// convertPath 将RDB路径转换为JumpServer路径格式
func convertPath(rdbPath string) string {
	if rdbPath == "" {
		return "/"
	}
	
	// 将点分隔转换为斜杠分隔
	parts := strings.Split(rdbPath, ".")
	return "/" + strings.Join(parts, "/")
}

// TestNodeUniquenessValidation 测试节点唯一性验证
func TestNodeUniquenessValidation(t *testing.T) {
	// 模拟两个同名但不同路径的节点
	nodes := []*jumpserver.Node{
		{
			ID:       "node-1",
			Key:      "开发组",
			Value:    "开发组",
			FullPath: "/公司/部门A/开发组",
			Parent:   "dept-a-id",
		},
		{
			ID:       "node-2", 
			Key:      "开发组",
			Value:    "开发组",
			FullPath: "/公司/部门B/开发组",
			Parent:   "dept-b-id",
		},
	}

	// 验证路径唯一性
	pathMap := make(map[string]*jumpserver.Node)
	for _, node := range nodes {
		if existing, exists := pathMap[node.FullPath]; exists {
			t.Errorf("发现重复路径: %s, 节点1=%s, 节点2=%s", 
				node.FullPath, existing.ID, node.ID)
		} else {
			pathMap[node.FullPath] = node
			t.Logf("✅ 唯一路径: %s -> 节点ID: %s", node.FullPath, node.ID)
		}
	}

	// 验证名称可以重复但路径必须唯一
	nameMap := make(map[string][]*jumpserver.Node)
	for _, node := range nodes {
		nameMap[node.Value] = append(nameMap[node.Value], node)
	}

	for name, nodeList := range nameMap {
		if len(nodeList) > 1 {
			t.Logf("✅ 同名节点 '%s' 有 %d 个，但路径不同:", name, len(nodeList))
			for _, node := range nodeList {
				t.Logf("   - ID: %s, 路径: %s", node.ID, node.FullPath)
			}
		}
	}
}
