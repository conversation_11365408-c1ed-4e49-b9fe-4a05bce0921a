package test

import (
	"os"
	"testing"
	"time"

	"github.com/toolkits/pkg/logger"

	"arboris/src/common/loggeri"
	"arboris/src/modules/jumpserver-sync/config"
)

// TestLoggerIntegration 测试logger集成
func TestLoggerIntegration(t *testing.T) {
	// 跳过测试，避免在CI中运行
	t.Ski<PERSON>("Skipping integration test")

	// 创建临时日志目录
	logDir := "test-logs"
	if err := os.MkdirAll(logDir, 0755); err != nil {
		t.Fatalf("Failed to create log directory: %v", err)
	}
	defer os.RemoveAll(logDir)

	// 初始化logger配置
	loggerConfig := loggeri.Config{
		Dir:       logDir,
		Level:     "DEBUG",
		KeepHours: 1,
	}
	loggeri.Init(loggerConfig)

	// 测试各种日志级别
	logger.Debug("This is a debug message")
	logger.Info("This is an info message")
	logger.Warn("This is a warning message")
	logger.Error("This is an error message")

	// 测试格式化日志
	logger.Infof("User %s created successfully with ID: %d", "testuser", 12345)
	logger.Errorf("Failed to connect to JumpServer: %v", "connection timeout")

	// 测试结构化日志
	logger.Infof("Event processed: type=%s, id=%d, status=%s", "user_create", 1001, "success")

	// 等待日志写入
	time.Sleep(100 * time.Millisecond)

	// 验证日志文件是否创建
	entries, err := os.ReadDir(logDir)
	if err != nil {
		t.Fatalf("Failed to read log directory: %v", err)
	}

	if len(entries) == 0 {
		t.Error("No log files were created")
	}

	for _, entry := range entries {
		t.Logf("Log file created: %s", entry.Name())
	}

	// 关闭logger
	logger.Close()

	t.Log("Logger integration test completed successfully")
}

// TestConfigParsing 测试配置解析
func TestConfigParsing(t *testing.T) {
	// 跳过测试，避免在CI中运行
	t.Skip("Skipping integration test")

	// 解析配置文件
	if err := config.Parse(); err != nil {
		t.Fatalf("Failed to parse config: %v", err)
	}

	// 验证logger配置
	if config.Config.Logger.Dir == "" {
		t.Error("Logger directory not configured")
	}

	if config.Config.Logger.Level == "" {
		t.Error("Logger level not configured")
	}

	if config.Config.Logger.KeepHours == 0 {
		t.Error("Logger keep hours not configured")
	}

	t.Logf("Logger config: dir=%s, level=%s, keepHours=%d",
		config.Config.Logger.Dir,
		config.Config.Logger.Level,
		config.Config.Logger.KeepHours)

	// 验证JumpServer配置
	if config.Config.JumpServer.BaseURL == "" {
		t.Error("JumpServer base URL not configured")
	}

	if config.Config.JumpServer.Username == "" && config.Config.JumpServer.Token == "" {
		t.Error("JumpServer authentication not configured")
	}

	t.Logf("JumpServer config: url=%s, username=%s, org=%s",
		config.Config.JumpServer.BaseURL,
		config.Config.JumpServer.Username,
		config.Config.JumpServer.Organization)

	// 验证同步配置
	if config.Config.Sync.Consumer.Stream == "" {
		t.Error("Consumer stream not configured")
	}

	if config.Config.Sync.Consumer.Group == "" {
		t.Error("Consumer group not configured")
	}

	t.Logf("Sync config: stream=%s, group=%s, consumer=%s",
		config.Config.Sync.Consumer.Stream,
		config.Config.Sync.Consumer.Group,
		config.Config.Sync.Consumer.Consumer)

	t.Log("Config parsing test completed successfully")
}

// TestLoggerWithRealConfig 使用真实配置测试logger
func TestLoggerWithRealConfig(t *testing.T) {
	// 跳过测试，避免在CI中运行
	t.Skip("Skipping integration test")

	// 解析配置文件
	if err := config.Parse(); err != nil {
		t.Fatalf("Failed to parse config: %v", err)
	}

	// 使用配置文件中的logger配置
	loggerConfig := loggeri.Config{
		Dir:       config.Config.Logger.Dir,
		Level:     config.Config.Logger.Level,
		KeepHours: config.Config.Logger.KeepHours,
	}

	// 创建日志目录
	if err := os.MkdirAll(config.Config.Logger.Dir, 0755); err != nil {
		t.Fatalf("Failed to create log directory: %v", err)
	}

	// 初始化logger
	loggeri.Init(loggerConfig)

	// 模拟jumpserver-sync服务的日志输出
	logger.Info("Starting JumpServer sync service")
	logger.Infof("Config loaded: jumpserver=%s, redis=%s",
		config.Config.JumpServer.BaseURL,
		config.Config.Redis.Addr)

	// 模拟事件处理日志
	logger.Infof("Processing event: type=%s, id=%s", "user_create", "user-001")
	logger.Infof("User created successfully: username=%s, js_id=%s", "testuser", "js-user-123")

	// 模拟错误日志
	logger.Errorf("Failed to sync user: %v", "connection timeout")
	logger.Warn("Retrying user sync operation")

	// 模拟调试日志
	logger.Debugf("Event data: %+v", map[string]interface{}{
		"type":     "user_create",
		"username": "testuser",
		"email":    "<EMAIL>",
	})

	// 等待日志写入
	time.Sleep(200 * time.Millisecond)

	// 关闭logger
	logger.Close()

	t.Log("Real config logger test completed successfully")
}

// TestLoggerPerformance 测试logger性能
func TestLoggerPerformance(t *testing.T) {
	// 跳过测试，避免在CI中运行
	t.Skip("Skipping performance test")

	// 创建临时日志目录
	logDir := "perf-test-logs"
	if err := os.MkdirAll(logDir, 0755); err != nil {
		t.Fatalf("Failed to create log directory: %v", err)
	}
	defer os.RemoveAll(logDir)

	// 初始化logger
	loggerConfig := loggeri.Config{
		Dir:       logDir,
		Level:     "INFO",
		KeepHours: 1,
	}
	loggeri.Init(loggerConfig)

	// 性能测试：大量日志写入
	start := time.Now()
	numLogs := 10000

	for i := 0; i < numLogs; i++ {
		logger.Infof("Performance test log entry %d: user=%s, action=%s, timestamp=%d",
			i, "testuser", "login", time.Now().Unix())
	}

	duration := time.Since(start)
	logsPerSecond := float64(numLogs) / duration.Seconds()

	logger.Infof("Performance test completed: %d logs in %v (%.2f logs/sec)",
		numLogs, duration, logsPerSecond)

	// 关闭logger
	logger.Close()

	t.Logf("Performance test: %d logs in %v (%.2f logs/sec)",
		numLogs, duration, logsPerSecond)

	if logsPerSecond < 1000 {
		t.Logf("Warning: Log performance is below 1000 logs/sec: %.2f", logsPerSecond)
	}
}
