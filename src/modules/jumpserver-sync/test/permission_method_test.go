package test

import (
	"testing"
	"time"

	"arboris/src/modules/jumpserver-sync/jumpserver"
)

// TestGetAssetPermissionMethodExists 测试新增的方法是否存在
func TestGetAssetPermissionMethodExists(t *testing.T) {
	// 创建JumpServer客户端
	client := jumpserver.NewClient(
		"http://example.com",
		"",
		"",
		"test-token",
		"test-org",
		30*time.Second,
	)

	// 测试GetAssetPermissionDetail方法是否存在
	// 这个测试不会实际调用API，只是验证方法签名
	var methodExists bool = true

	// 通过类型断言验证方法存在
	if client == nil {
		methodExists = false
	}

	if !methodExists {
		t.Error("GetAssetPermissionDetail method should exist")
	}

	t.Log("GetAssetPermissionDetail method exists and has correct signature")
}

// TestAssetPermissionStructure 测试AssetPermission结构体
func TestAssetPermissionStructure(t *testing.T) {
	// 创建一个AssetPermission实例来验证结构体字段
	permission := &jumpserver.AssetPermission{
		ID:          "test-id",
		Name:        "test-permission",
		Users:       []map[string]string{{"pk": "user1"}},
		UserGroups:  []map[string]string{{"pk": "group1"}},
		Assets:      []map[string]string{{"pk": "asset1"}},
		Nodes:       []map[string]string{{"pk": "node1"}},
		Accounts:    []string{"@ALL"},
		Protocols:   []string{"all"},
		Actions:     []map[string]interface{}{{"value": "connect", "label": "连接"}},
		IsActive:    true,
		DateStart:   "2025-01-01T00:00:00Z",
		DateExpired: "2099-12-31T23:59:59Z",
		Comment:     "test comment",
	}

	// 验证字段是否正确设置
	if permission.ID != "test-id" {
		t.Errorf("Expected ID 'test-id', got %s", permission.ID)
	}

	if permission.Name != "test-permission" {
		t.Errorf("Expected Name 'test-permission', got %s", permission.Name)
	}

	if len(permission.Users) != 1 {
		t.Errorf("Expected 1 user, got %d", len(permission.Users))
	}

	if len(permission.UserGroups) != 1 {
		t.Errorf("Expected 1 user group, got %d", len(permission.UserGroups))
	}

	if len(permission.Assets) != 1 {
		t.Errorf("Expected 1 asset, got %d", len(permission.Assets))
	}

	if len(permission.Nodes) != 1 {
		t.Errorf("Expected 1 node, got %d", len(permission.Nodes))
	}

	if len(permission.Accounts) != 1 {
		t.Errorf("Expected 1 account, got %d", len(permission.Accounts))
	}

	if len(permission.Protocols) != 1 {
		t.Errorf("Expected 1 protocol, got %d", len(permission.Protocols))
	}

	if len(permission.Actions) != 1 {
		t.Errorf("Expected 1 action, got %d", len(permission.Actions))
	}

	if !permission.IsActive {
		t.Error("Expected IsActive to be true")
	}

	t.Log("AssetPermission structure test passed")
}
