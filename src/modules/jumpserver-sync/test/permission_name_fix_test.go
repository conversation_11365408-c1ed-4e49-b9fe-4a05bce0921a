package test

import (
	"strings"
	"testing"
)

// 模拟节点名称映射
var mockNodeNameMapping = map[string]string{
	"公司":     "公司",
	"部门A":    "部门A",
	"部门B":    "部门B",
	"开发组":    "开发组",
	"测试组":    "测试组",
	"技术部":    "技术部",
	"inner":   "内网",
	"code":    "代码仓库",
	"master":  "主分支",
}

// TestPermissionNameGeneration 测试权限名称生成的修复
func TestPermissionNameGeneration(t *testing.T) {
	testCases := []struct {
		name         string
		nodePath     string
		roleName     string
		expectedName string
		description  string
	}{
		{
			name:         "部门A下的开发组",
			nodePath:     "公司.部门A.开发组",
			roleName:     "管理员",
			expectedName: "RDB_公司_部门A_开发组_管理员",
			description:  "第一个开发组的权限名称（使用节点名称）",
		},
		{
			name:         "部门B下的开发组",
			nodePath:     "公司.部门B.开发组",
			roleName:     "管理员",
			expectedName: "RDB_公司_部门B_开发组_管理员",
			description:  "第二个开发组的权限名称（同名但不同路径，使用节点名称）",
		},
		{
			name:         "英文标识路径",
			nodePath:     "inner.code.master",
			roleName:     "developer",
			expectedName: "RDB_内网_代码仓库_主分支_developer",
			description:  "英文标识转换为中文名称的权限名称",
		},
		{
			name:         "包含空格的角色",
			nodePath:     "公司.技术部",
			roleName:     "高级 管理员",
			expectedName: "RDB_公司_技术部_高级_管理员",
			description:  "角色名称包含空格",
		},
		{
			name:         "根节点",
			nodePath:     "",
			roleName:     "超级管理员",
			expectedName: "RDB_根节点_超级管理员",
			description:  "根节点权限",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 模拟权限名称生成逻辑（先转换为节点名称路径）
			nodeNamePath := getNodeNamePathForTest(tc.nodePath)
			permissionName := generatePermissionNameForTest(nodeNamePath, tc.roleName)

			if permissionName != tc.expectedName {
				t.Errorf("权限名称生成错误:\n输入: 路径=%s, 角色=%s\n节点名称路径: %s\n期望: %s\n实际: %s",
					tc.nodePath, tc.roleName, nodeNamePath, tc.expectedName, permissionName)
			} else {
				t.Logf("✅ %s: %s (节点名称路径: %s)", tc.description, permissionName, nodeNamePath)
			}
		})
	}
}

// TestPermissionNameUniqueness 测试权限名称唯一性
func TestPermissionNameUniqueness(t *testing.T) {
	// 模拟同名节点的不同权限
	permissions := []struct {
		nodePath string
		roleName string
	}{
		{"公司.部门A.开发组", "管理员"},
		{"公司.部门B.开发组", "管理员"},
		{"公司.部门A.测试组", "管理员"},
		{"公司.部门B.测试组", "管理员"},
		{"公司.部门A.开发组", "普通用户"},
		{"公司.部门B.开发组", "普通用户"},
	}

	permissionNames := make(map[string][]string)
	
	for _, perm := range permissions {
		nodeNamePath := getNodeNamePathForTest(perm.nodePath)
		name := generatePermissionNameForTest(nodeNamePath, perm.roleName)
		key := perm.nodePath + ":" + perm.roleName
		permissionNames[name] = append(permissionNames[name], key)
	}

	// 检查是否有重复的权限名称
	for name, sources := range permissionNames {
		if len(sources) > 1 {
			t.Errorf("发现重复的权限名称: %s\n来源: %v", name, sources)
		} else {
			t.Logf("✅ 唯一权限名称: %s", name)
		}
	}
}

// TestPermissionNameBeforeAndAfterFix 对比修复前后的权限名称
func TestPermissionNameBeforeAndAfterFix(t *testing.T) {
	testCases := []struct {
		nodePath string
		nodeName string
		roleName string
	}{
		{"公司.部门A.开发组", "开发组", "管理员"},
		{"公司.部门B.开发组", "开发组", "管理员"},
		{"公司.部门A.测试组", "测试组", "管理员"},
		{"公司.部门B.测试组", "测试组", "管理员"},
	}

	t.Log("=== 修复前的权限名称（会有冲突）===")
	oldNames := make(map[string]int)
	for _, tc := range testCases {
		// 模拟修复前的逻辑：只使用节点名称
		oldName := generatePermissionNameForTest(tc.nodeName, tc.roleName)
		oldNames[oldName]++
		t.Logf("路径: %s -> 权限名称: %s", tc.nodePath, oldName)
	}

	// 检查修复前的冲突
	conflicts := 0
	for name, count := range oldNames {
		if count > 1 {
			t.Logf("⚠️  冲突: %s 出现了 %d 次", name, count)
			conflicts++
		}
	}

	t.Log("=== 修复后的权限名称（无冲突）===")
	newNames := make(map[string]int)
	for _, tc := range testCases {
		// 修复后的逻辑：使用节点名称路径
		nodeNamePath := getNodeNamePathForTest(tc.nodePath)
		newName := generatePermissionNameForTest(nodeNamePath, tc.roleName)
		newNames[newName]++
		t.Logf("路径: %s -> 节点名称路径: %s -> 权限名称: %s", tc.nodePath, nodeNamePath, newName)
	}

	// 检查修复后是否还有冲突
	newConflicts := 0
	for name, count := range newNames {
		if count > 1 {
			t.Errorf("❌ 修复后仍有冲突: %s 出现了 %d 次", name, count)
			newConflicts++
		}
	}

	t.Logf("修复前冲突数: %d, 修复后冲突数: %d", conflicts, newConflicts)
	
	if conflicts > 0 && newConflicts == 0 {
		t.Log("✅ 权限名称冲突问题已成功修复！")
	} else if conflicts == 0 {
		t.Log("ℹ️  原本就没有冲突")
	} else {
		t.Error("❌ 修复失败，仍存在冲突")
	}
}

// generatePermissionNameForTest 测试用的权限名称生成函数
func generatePermissionNameForTest(nodePath, roleName string) string {
	// 处理 RDB 路径格式（如：inner.code.master）
	// 将点分隔转换为下划线分隔，确保权限名称的唯一性
	cleanPath := strings.ReplaceAll(nodePath, ".", "_")
	cleanPath = strings.ReplaceAll(cleanPath, "/", "_")
	cleanPath = strings.ReplaceAll(cleanPath, " ", "_")
	cleanPath = strings.Trim(cleanPath, "_")

	if cleanPath == "" {
		cleanPath = "root"
	}

	// 清理角色名称中的特殊字符
	cleanRole := strings.ReplaceAll(roleName, " ", "_")
	cleanRole = strings.ReplaceAll(cleanRole, "/", "_")

	return "RDB_" + cleanPath + "_" + cleanRole
}

// getNodeNamePathForTest 测试用的节点名称路径转换函数
func getNodeNamePathForTest(nodePath string) string {
	if nodePath == "" {
		return "根节点"
	}

	// 分解路径
	parts := strings.Split(nodePath, ".")
	nameparts := make([]string, 0, len(parts))

	// 逐级转换为节点名称
	for _, part := range parts {
		if name, exists := mockNodeNameMapping[part]; exists {
			nameparts = append(nameparts, name)
		} else {
			// 如果映射不存在，使用原始标识
			nameparts = append(nameparts, part)
		}
	}

	return strings.Join(nameparts, ".")
}

// TestPermissionNameCharacterHandling 测试特殊字符处理
func TestPermissionNameCharacterHandling(t *testing.T) {
	testCases := []struct {
		nodePath     string
		roleName     string
		expectedName string
		description  string
	}{
		{
			nodePath:     "公司/部门A/开发组",
			roleName:     "管理员",
			expectedName: "RDB_公司_部门A_开发组_管理员",
			description:  "路径包含斜杠",
		},
		{
			nodePath:     "公司.部门 A.开发 组",
			roleName:     "高级 管理员",
			expectedName: "RDB_公司_部门_A_开发_组_高级_管理员",
			description:  "路径和角色都包含空格",
		},
		{
			nodePath:     "inner.code.master",
			roleName:     "dev/ops",
			expectedName: "RDB_内网_代码仓库_主分支_dev_ops",
			description:  "角色名称包含斜杠",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.description, func(t *testing.T) {
			nodeNamePath := getNodeNamePathForTest(tc.nodePath)
			permissionName := generatePermissionNameForTest(nodeNamePath, tc.roleName)

			if permissionName != tc.expectedName {
				t.Errorf("特殊字符处理错误:\n输入: 路径=%s, 角色=%s\n节点名称路径: %s\n期望: %s\n实际: %s",
					tc.nodePath, tc.roleName, nodeNamePath, tc.expectedName, permissionName)
			} else {
				t.Logf("✅ %s: %s (节点名称路径: %s)", tc.description, permissionName, nodeNamePath)
			}
		})
	}
}
