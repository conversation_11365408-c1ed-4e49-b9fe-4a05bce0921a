package test

import (
	"fmt"
	"log"
	"strings"
	"testing"

	"arboris/src/modules/jumpserver-sync/jumpserver"
)

// TestJumpServerNodePathFormat 测试JumpServer节点路径格式兼容性
func TestJumpServerNodePathFormat(t *testing.T) {
	// 模拟JumpServer客户端（需要实际的JumpServer环境）
	// client := jumpserver.NewClient("http://jumpserver.example.com", "admin", "password")

	testCases := []struct {
		rdbPath      string
		expectedPath string
		description  string
	}{
		{
			rdbPath:      "inner.code.master",
			expectedPath: "/inner/code/master",
			description:  "点分隔转斜杠分隔",
		},
		{
			rdbPath:      "inner.ops.production",
			expectedPath: "/inner/ops/production",
			description:  "运维生产环境节点",
		},
		{
			rdbPath:      "inner.data.mysql",
			expectedPath: "/inner/data/mysql",
			description:  "数据库节点",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.description, func(t *testing.T) {
			converted := convertRDBPathToJSPath(tc.rdbPath)
			if converted != tc.expectedPath {
				t.<PERSON>("路径转换失败: 输入=%s, 期望=%s, 实际=%s", 
					tc.rdbPath, tc.expectedPath, converted)
			} else {
				t.Logf("路径转换成功: %s -> %s", tc.rdbPath, converted)
			}
		})
	}
}

// convertRDBPathToJSPath 将RDB路径转换为JumpServer路径格式
func convertRDBPathToJSPath(rdbPath string) string {
	// inner.code.master -> /inner/code/master
	if rdbPath == "" {
		return "/"
	}
	
	// 将点分隔转换为斜杠分隔
	parts := strings.Split(rdbPath, ".")
	return "/" + strings.Join(parts, "/")
}

// TestJumpServerNodeCreation 测试JumpServer节点创建
func TestJumpServerNodeCreation(t *testing.T) {
	// 这个测试需要实际的JumpServer环境
	t.Skip("需要实际的JumpServer环境进行测试")

	// 示例代码：
	/*
	client := jumpserver.NewClient("http://jumpserver.example.com", "admin", "password")
	
	testNodes := []struct {
		path string
		name string
	}{
		{"/inner", "Inner"},
		{"/inner/code", "Code"},
		{"/inner/code/master", "Master"},
		{"/inner/ops", "Operations"},
		{"/inner/ops/production", "Production"},
	}

	for _, node := range testNodes {
		jsNode := &jumpserver.Node{
			Key:      extractNodeKey(node.path),
			Value:    node.name,
			Parent:   extractParentPath(node.path),
			FullPath: node.path,
		}

		createdNode, err := client.CreateNode(jsNode)
		if err != nil {
			t.Errorf("创建节点失败: path=%s, error=%v", node.path, err)
		} else {
			t.Logf("创建节点成功: path=%s, id=%s", node.path, createdNode.ID)
		}
	}
	*/
}

// extractNodeKey 从路径中提取节点key
func extractNodeKey(path string) string {
	parts := strings.Split(strings.Trim(path, "/"), "/")
	if len(parts) == 0 {
		return ""
	}
	return parts[len(parts)-1]
}

// extractParentPath 从路径中提取父路径
func extractParentPath(path string) string {
	if path == "/" || path == "" {
		return ""
	}
	
	parts := strings.Split(strings.Trim(path, "/"), "/")
	if len(parts) <= 1 {
		return ""
	}
	
	return "/" + strings.Join(parts[:len(parts)-1], "/")
}

// 实际的路径转换函数，用于生产环境
func ConvertRDBPathToJumpServerPath(rdbPath string) string {
	if rdbPath == "" {
		return "/"
	}
	
	// inner.code.master -> /inner/code/master
	parts := strings.Split(rdbPath, ".")
	return "/" + strings.Join(parts, "/")
}

// 创建JumpServer节点的辅助函数
func CreateJumpServerNodeHierarchy(client *jumpserver.Client, rdbPath string) error {
	jsPath := ConvertRDBPathToJumpServerPath(rdbPath)
	
	// 分解路径，逐级创建节点
	parts := strings.Split(strings.Trim(jsPath, "/"), "/")
	
	currentPath := ""
	for i, part := range parts {
		if part == "" {
			continue
		}
		
		currentPath += "/" + part
		parentPath := ""
		if i > 0 {
			parentParts := parts[:i]
			parentPath = "/" + strings.Join(parentParts, "/")
		}
		
		// 检查节点是否已存在
		existingNode, err := client.GetNodeByPath(currentPath)
		if err == nil && existingNode != nil {
			logger.Infof("节点已存在: %s", currentPath)
			continue
		}
		
		// 创建新节点
		node := &jumpserver.Node{
			Key:      part,
			Value:    strings.Title(part), // 首字母大写作为显示名
			Parent:   parentPath,
			FullPath: currentPath,
		}
		
		createdNode, err := client.CreateNode(node)
		if err != nil {
			return fmt.Errorf("创建节点失败: path=%s, error=%v", currentPath, err)
		}
		
		logger.Infof("创建节点成功: path=%s, id=%s", currentPath, createdNode.ID)
	}
	
	return nil
}

// 示例：如何在权限同步中使用路径转换
func ExamplePermissionSync() {
	rdbNodePath := "inner.ops.production"
	jsNodePath := ConvertRDBPathToJumpServerPath(rdbNodePath)
	
	fmt.Printf("RDB节点路径: %s\n", rdbNodePath)
	fmt.Printf("JumpServer节点路径: %s\n", jsNodePath)
	
	// 输出:
	// RDB节点路径: inner.ops.production
	// JumpServer节点路径: /inner/ops/production
}
