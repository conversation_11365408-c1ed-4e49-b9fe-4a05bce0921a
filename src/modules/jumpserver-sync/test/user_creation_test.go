package test

import (
	"fmt"
	"log"
	"testing"
	"time"

	"arboris/src/modules/jumpserver-sync/jumpserver"
)

// TestUserCreationTwoStep 测试用户创建的两步操作
func TestUserCreationTwoStep(t *testing.T) {
	// 跳过测试，避免在CI中运行
	t.Ski<PERSON>("Skipping integration test")

	// 创建JumpServer客户端
	client := jumpserver.NewClient(
		"http://**********:8090", // JumpServer地址
		"admin",                  // 用户名
		"root.2025",             // 密码
		"",                      // Token（如果有的话）
		"Default",               // 组织
		30*time.Second,          // 超时时间
	)

	// 测试连接
	if err := client.TestConnection(); err != nil {
		t.Fatalf("Failed to connect to JumpServer: %v", err)
	}

	// 测试用户数据
	testUser := &jumpserver.User{
		Username:    "test-user-001",
		Name:        "测试用户001",
		Email:       "<EMAIL>",
		Password:    "TestPassword123!",
		IsActive:    true,
		IsSuperuser: false,
		Comment:     "测试用户 - 两步创建",
		Source:      "rdb",
	}

	// 测试方法1：CreateUser（不设置密码）
	logger.Infof("Testing CreateUser (without password)...")
	createdUser1, err := client.CreateUser(testUser)
	if err != nil {
		t.Fatalf("Failed to create user without password: %v", err)
	}
	logger.Infof("Created user without password: %+v", createdUser1)

	// 手动设置密码
	err = client.ChangeUserPassword(createdUser1.ID, testUser.Password)
	if err != nil {
		t.Fatalf("Failed to set password manually: %v", err)
	}
	logger.Infof("Password set manually for user: %s", createdUser1.Username)

	// 清理第一个测试用户
	err = client.DeleteUser(createdUser1.ID)
	if err != nil {
		t.Fatalf("Failed to delete test user 1: %v", err)
	}

	// 测试方法2：CreateUserWithPassword（两步操作）
	logger.Infof("Testing CreateUserWithPassword (two-step operation)...")
	testUser.Username = "test-user-002"
	testUser.Email = "<EMAIL>"
	
	createdUser2, err := client.CreateUserWithPassword(testUser)
	if err != nil {
		t.Fatalf("Failed to create user with password: %v", err)
	}
	logger.Infof("Created user with password: %+v", createdUser2)

	// 验证用户是否创建成功
	foundUser, err := client.GetUser(createdUser2.Username)
	if err != nil {
		t.Fatalf("Failed to get created user: %v", err)
	}
	logger.Infof("Found created user: %+v", foundUser)

	// 清理第二个测试用户
	err = client.DeleteUser(createdUser2.ID)
	if err != nil {
		t.Fatalf("Failed to delete test user 2: %v", err)
	}

	logger.Infof("User creation two-step test completed successfully")
}

// TestPasswordChange 测试密码修改功能
func TestPasswordChange(t *testing.T) {
	// 跳过测试，避免在CI中运行
	t.Skip("Skipping integration test")

	// 创建JumpServer客户端
	client := jumpserver.NewClient(
		"http://**********:8090", // JumpServer地址
		"admin",                  // 用户名
		"root.2025",             // 密码
		"",                      // Token（如果有的话）
		"Default",               // 组织
		30*time.Second,          // 超时时间
	)

	// 测试连接
	if err := client.TestConnection(); err != nil {
		t.Fatalf("Failed to connect to JumpServer: %v", err)
	}

	// 创建测试用户
	testUser := &jumpserver.User{
		Username:    "test-pwd-user",
		Name:        "密码测试用户",
		Email:       "<EMAIL>",
		Password:    "InitialPassword123!",
		IsActive:    true,
		IsSuperuser: false,
		Comment:     "密码修改测试用户",
		Source:      "rdb",
	}

	createdUser, err := client.CreateUserWithPassword(testUser)
	if err != nil {
		t.Fatalf("Failed to create test user: %v", err)
	}
	defer func() {
		// 清理测试用户
		client.DeleteUser(createdUser.ID)
	}()

	logger.Infof("Created test user for password change: %s", createdUser.Username)

	// 测试密码修改
	newPassword := "NewPassword456!"
	err = client.ChangeUserPassword(createdUser.ID, newPassword)
	if err != nil {
		t.Fatalf("Failed to change user password: %v", err)
	}

	logger.Infof("Password changed successfully for user: %s", createdUser.Username)

	// 再次修改密码
	anotherPassword := "AnotherPassword789!"
	err = client.ChangeUserPassword(createdUser.ID, anotherPassword)
	if err != nil {
		t.Fatalf("Failed to change user password again: %v", err)
	}

	logger.Infof("Password changed again successfully for user: %s", createdUser.Username)
}

// TestRDBUserSync 测试RDB用户同步流程
func TestRDBUserSync(t *testing.T) {
	// 跳过测试，避免在CI中运行
	t.Skip("Skipping integration test")

	// 模拟RDB用户数据
	users := []struct {
		username     string
		dispname     string
		email        string
		password     string
		status       int
		isRoot       int
		organization string
	}{
		{"testuser1", "测试用户1", "<EMAIL>", "Password123!", 0, 0, "default"},
		{"testuser2", "测试用户2", "<EMAIL>", "Password456!", 0, 0, "default"},
		{"testadmin", "测试管理员", "<EMAIL>", "AdminPass789!", 0, 1, "default"},
	}

	// 这里可以测试完整的用户同步流程
	// 1. 创建事件生产者
	// 2. 发布用户创建事件
	// 3. 验证JumpServer中的用户创建
	// 4. 发布密码变更事件
	// 5. 验证JumpServer中的密码更新
	// 6. 发布用户更新事件
	// 7. 验证JumpServer中的用户信息更新

	for _, user := range users {
		fmt.Printf("User: Username=%s, Dispname=%s, Email=%s, Status=%d, IsRoot=%d, Org=%s\n",
			user.username, user.dispname, user.email, user.status, user.isRoot, user.organization)
	}
}

// TestUserCreationErrorHandling 测试用户创建的错误处理
func TestUserCreationErrorHandling(t *testing.T) {
	// 跳过测试，避免在CI中运行
	t.Skip("Skipping integration test")

	// 创建JumpServer客户端
	client := jumpserver.NewClient(
		"http://**********:8090", // JumpServer地址
		"admin",                  // 用户名
		"root.2025",             // 密码
		"",                      // Token（如果有的话）
		"Default",               // 组织
		30*time.Second,          // 超时时间
	)

	// 测试连接
	if err := client.TestConnection(); err != nil {
		t.Fatalf("Failed to connect to JumpServer: %v", err)
	}

	// 测试1：创建重复用户名的用户
	testUser := &jumpserver.User{
		Username:    "admin", // 使用已存在的用户名
		Name:        "重复用户",
		Email:       "<EMAIL>",
		Password:    "Password123!",
		IsActive:    true,
		IsSuperuser: false,
		Comment:     "重复用户名测试",
		Source:      "rdb",
	}

	_, err := client.CreateUserWithPassword(testUser)
	if err == nil {
		t.Errorf("Expected error when creating user with duplicate username, but got none")
	} else {
		logger.Infof("Expected error for duplicate username: %v", err)
	}

	// 测试2：创建无效邮箱的用户
	testUser.Username = "invalid-email-user"
	testUser.Email = "invalid-email" // 无效邮箱格式

	_, err = client.CreateUserWithPassword(testUser)
	if err == nil {
		t.Errorf("Expected error when creating user with invalid email, but got none")
	} else {
		logger.Infof("Expected error for invalid email: %v", err)
	}

	// 测试3：为不存在的用户修改密码
	err = client.ChangeUserPassword("non-existent-user-id", "NewPassword123!")
	if err == nil {
		t.Errorf("Expected error when changing password for non-existent user, but got none")
	} else {
		logger.Infof("Expected error for non-existent user: %v", err)
	}

	logger.Infof("Error handling tests completed")
}
