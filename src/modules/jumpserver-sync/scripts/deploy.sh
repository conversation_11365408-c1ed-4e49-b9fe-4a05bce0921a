#!/bin/bash

# JumpServer同步服务部署脚本

set -e

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../.." && pwd)"
SYNC_MODULE_DIR="$PROJECT_ROOT/src/modules/jumpserver-sync"
RDB_MODULE_DIR="$PROJECT_ROOT/src/modules/rdb"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_step "检查依赖..."
    
    # 检查Go
    if ! command -v go &> /dev/null; then
        log_error "Go未安装，请先安装Go 1.19+"
        exit 1
    fi
    
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    log_info "Go版本: $GO_VERSION"
    
    # 检查Redis
    if ! command -v redis-cli &> /dev/null; then
        log_warn "redis-cli未安装，建议安装Redis客户端用于调试"
    fi
    
    # 检查Make
    if ! command -v make &> /dev/null; then
        log_warn "make未安装，将使用go build直接编译"
    fi
}

# 创建必要的目录
create_directories() {
    log_step "创建目录结构..."
    
    mkdir -p "$SYNC_MODULE_DIR/logs"
    mkdir -p "$SYNC_MODULE_DIR/build"
    mkdir -p "$SYNC_MODULE_DIR/dist"
    
    log_info "目录创建完成"
}

# 编译JumpServer同步服务
build_sync_service() {
    log_step "编译JumpServer同步服务..."
    
    cd "$SYNC_MODULE_DIR"
    
    if [ -f "Makefile" ] && command -v make &> /dev/null; then
        log_info "使用Makefile编译..."
        make clean
        make build
    else
        log_info "使用go build编译..."
        go mod tidy
        go build -o build/jumpserver-sync .
    fi
    
    if [ -f "build/jumpserver-sync" ]; then
        log_info "JumpServer同步服务编译成功"
    else
        log_error "JumpServer同步服务编译失败"
        exit 1
    fi
}

# 检查配置文件
check_config() {
    log_step "检查配置文件..."
    
    # 检查JumpServer同步服务配置
    SYNC_CONFIG="$SYNC_MODULE_DIR/etc/jumpserver-sync.yml"
    if [ ! -f "$SYNC_CONFIG" ]; then
        log_warn "JumpServer同步服务配置文件不存在，将创建示例配置"
        cp "$SYNC_CONFIG" "$SYNC_MODULE_DIR/jumpserver-sync.yml" 2>/dev/null || true
    fi
    
    # 检查RDB配置
    RDB_CONFIG="$PROJECT_ROOT/etc/rdb.yml"
    if [ ! -f "$RDB_CONFIG" ]; then
        log_error "RDB配置文件不存在: $RDB_CONFIG"
        exit 1
    fi
    
    # 检查RDB配置中是否包含Redis和Events配置
    if ! grep -q "redis:" "$RDB_CONFIG"; then
        log_warn "RDB配置文件中缺少Redis配置，请手动添加"
    fi
    
    if ! grep -q "events:" "$RDB_CONFIG"; then
        log_warn "RDB配置文件中缺少Events配置，请手动添加"
    fi
    
    log_info "配置文件检查完成"
}

# 启动Redis（如果需要）
start_redis() {
    log_step "检查Redis服务..."
    
    if redis-cli ping > /dev/null 2>&1; then
        log_info "Redis服务已运行"
    else
        log_warn "Redis服务未运行"
        
        if command -v redis-server &> /dev/null; then
            log_info "尝试启动Redis服务..."
            redis-server --daemonize yes
            sleep 2
            
            if redis-cli ping > /dev/null 2>&1; then
                log_info "Redis服务启动成功"
            else
                log_error "Redis服务启动失败"
                exit 1
            fi
        else
            log_error "Redis未安装，请先安装Redis"
            exit 1
        fi
    fi
}

# 启动JumpServer同步服务
start_sync_service() {
    log_step "启动JumpServer同步服务..."
    
    cd "$SYNC_MODULE_DIR"
    
    # 检查是否已有进程在运行
    if pgrep -f "jumpserver-sync" > /dev/null; then
        log_warn "JumpServer同步服务已在运行，正在停止..."
        pkill -f "jumpserver-sync" || true
        sleep 2
    fi
    
    # 启动服务
    CONFIG_FILE="jumpserver-sync.yml"
    if [ ! -f "$CONFIG_FILE" ]; then
        CONFIG_FILE="etc/jumpserver-sync.yml"
    fi
    
    log_info "使用配置文件: $CONFIG_FILE"
    nohup ./build/jumpserver-sync -f "$CONFIG_FILE" > logs/jumpserver-sync.log 2>&1 &
    
    sleep 3
    
    # 检查服务是否启动成功
    if pgrep -f "jumpserver-sync" > /dev/null; then
        log_info "JumpServer同步服务启动成功"
        
        # 检查健康状态
        if curl -s http://localhost:8080/health > /dev/null 2>&1; then
            log_info "服务健康检查通过"
        else
            log_warn "服务健康检查失败，请检查日志"
        fi
    else
        log_error "JumpServer同步服务启动失败，请检查日志"
        tail -n 20 logs/jumpserver-sync.log
        exit 1
    fi
}

# 显示服务状态
show_status() {
    log_step "显示服务状态..."
    
    echo ""
    echo "=== 服务状态 ==="
    
    # JumpServer同步服务状态
    if pgrep -f "jumpserver-sync" > /dev/null; then
        echo -e "${GREEN}✓${NC} JumpServer同步服务: 运行中"
        PID=$(pgrep -f "jumpserver-sync")
        echo "  PID: $PID"
        echo "  配置: $SYNC_MODULE_DIR/jumpserver-sync.yml"
        echo "  日志: $SYNC_MODULE_DIR/logs/jumpserver-sync.log"
        echo "  健康检查: http://localhost:8080/health"
    else
        echo -e "${RED}✗${NC} JumpServer同步服务: 未运行"
    fi
    
    # Redis状态
    if redis-cli ping > /dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} Redis服务: 运行中"
    else
        echo -e "${RED}✗${NC} Redis服务: 未运行"
    fi
    
    echo ""
    echo "=== 有用的命令 ==="
    echo "查看同步服务日志: tail -f $SYNC_MODULE_DIR/logs/jumpserver-sync.log"
    echo "查看服务信息: curl http://localhost:8080/info"
    echo "查看Redis流: redis-cli XINFO STREAM arboris:sync:events"
    echo "停止同步服务: pkill -f jumpserver-sync"
    echo "重启同步服务: $0 --restart"
    echo ""
}

# 停止服务
stop_services() {
    log_step "停止服务..."
    
    if pgrep -f "jumpserver-sync" > /dev/null; then
        log_info "停止JumpServer同步服务..."
        pkill -f "jumpserver-sync"
        sleep 2
        log_info "JumpServer同步服务已停止"
    else
        log_info "JumpServer同步服务未运行"
    fi
}

# 重启服务
restart_services() {
    log_step "重启服务..."
    stop_services
    sleep 2
    start_sync_service
    show_status
}

# 显示帮助
show_help() {
    echo "JumpServer同步服务部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help      显示帮助信息"
    echo "  --build-only    仅编译，不启动服务"
    echo "  --start-only    仅启动服务，不编译"
    echo "  --stop          停止服务"
    echo "  --restart       重启服务"
    echo "  --status        显示服务状态"
    echo ""
    echo "示例:"
    echo "  $0                # 完整部署（编译+启动）"
    echo "  $0 --build-only   # 仅编译"
    echo "  $0 --start-only   # 仅启动"
    echo "  $0 --status       # 查看状态"
}

# 主函数
main() {
    echo "=== JumpServer同步服务部署 ==="
    echo "项目根目录: $PROJECT_ROOT"
    echo "同步模块目录: $SYNC_MODULE_DIR"
    echo ""
    
    case "${1:-}" in
        --build-only)
            check_dependencies
            create_directories
            build_sync_service
            ;;
        --start-only)
            check_config
            start_redis
            start_sync_service
            show_status
            ;;
        --stop)
            stop_services
            ;;
        --restart)
            restart_services
            ;;
        --status)
            show_status
            ;;
        -h|--help)
            show_help
            ;;
        "")
            # 完整部署
            check_dependencies
            create_directories
            build_sync_service
            check_config
            start_redis
            start_sync_service
            show_status
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
