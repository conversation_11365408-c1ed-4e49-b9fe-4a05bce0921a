#!/bin/bash

# JumpServer同步服务集成测试脚本

set -e

echo "=== JumpServer同步服务集成测试 ==="

# 配置
REDIS_HOST="127.0.0.1"
REDIS_PORT="6379"
STREAM_NAME="arboris:sync:events"
SYNC_SERVICE_URL="http://localhost:8080"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Redis连接
check_redis() {
    log_info "检查Redis连接..."
    if redis-cli -h $REDIS_HOST -p $REDIS_PORT ping > /dev/null 2>&1; then
        log_info "Redis连接正常"
    else
        log_error "Redis连接失败，请检查Redis服务是否启动"
        exit 1
    fi
}

# 检查JumpServer同步服务
check_sync_service() {
    log_info "检查JumpServer同步服务..."
    if curl -s $SYNC_SERVICE_URL/health > /dev/null 2>&1; then
        log_info "JumpServer同步服务运行正常"
        curl -s $SYNC_SERVICE_URL/info | jq '.' || echo "服务信息获取成功"
    else
        log_error "JumpServer同步服务连接失败，请检查服务是否启动"
        exit 1
    fi
}

# 检查Redis Stream
check_stream() {
    log_info "检查Redis Stream..."
    
    # 检查流是否存在
    if redis-cli -h $REDIS_HOST -p $REDIS_PORT EXISTS $STREAM_NAME | grep -q "1"; then
        log_info "Redis Stream '$STREAM_NAME' 存在"
        
        # 获取流信息
        log_info "流信息:"
        redis-cli -h $REDIS_HOST -p $REDIS_PORT XINFO STREAM $STREAM_NAME || true
        
        # 获取消费者组信息
        log_info "消费者组信息:"
        redis-cli -h $REDIS_HOST -p $REDIS_PORT XINFO GROUPS $STREAM_NAME || log_warn "暂无消费者组"
    else
        log_warn "Redis Stream '$STREAM_NAME' 不存在，可能还没有事件发布"
    fi
}

# 发布测试事件
publish_test_event() {
    log_info "发布测试事件..."
    
    # 使用redis-cli发布一个测试事件
    EVENT_ID=$(date +%s%N)
    redis-cli -h $REDIS_HOST -p $REDIS_PORT XADD $STREAM_NAME "*" \
        "id" "test-$EVENT_ID" \
        "type" "node.create" \
        "source" "test" \
        "timestamp" "$(date +%s)" \
        "data.id" "999" \
        "data.name" "测试节点" \
        "data.path" "/test/node" \
        "metadata.test" "true"
    
    log_info "测试事件已发布，事件ID: test-$EVENT_ID"
}

# 检查事件消费
check_event_consumption() {
    log_info "等待事件消费..."
    sleep 3
    
    # 检查最新的几条消息
    log_info "最新的流消息:"
    redis-cli -h $REDIS_HOST -p $REDIS_PORT XREVRANGE $STREAM_NAME + - COUNT 5 || true
}

# 检查同步服务日志
check_sync_logs() {
    log_info "检查同步服务处理情况..."
    
    # 这里可以检查日志文件或通过API获取处理状态
    if [ -f "logs/jumpserver-sync.log" ]; then
        log_info "最近的同步日志:"
        tail -n 10 logs/jumpserver-sync.log || true
    else
        log_warn "同步服务日志文件不存在"
    fi
}

# 清理测试数据
cleanup() {
    log_info "清理测试数据..."
    
    # 可以选择性地清理测试事件
    # redis-cli -h $REDIS_HOST -p $REDIS_PORT DEL $STREAM_NAME
    
    log_info "测试完成"
}

# 主测试流程
main() {
    echo "开始集成测试..."
    echo "时间: $(date)"
    echo "Redis: $REDIS_HOST:$REDIS_PORT"
    echo "Stream: $STREAM_NAME"
    echo "同步服务: $SYNC_SERVICE_URL"
    echo ""
    
    # 执行检查
    check_redis
    check_sync_service
    check_stream
    
    # 发布测试事件
    publish_test_event
    
    # 检查消费情况
    check_event_consumption
    check_sync_logs
    
    # 清理
    cleanup
    
    log_info "集成测试完成！"
}

# 帮助信息
show_help() {
    echo "JumpServer同步服务集成测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  --redis-host   Redis主机地址 (默认: 127.0.0.1)"
    echo "  --redis-port   Redis端口 (默认: 6379)"
    echo "  --stream       Stream名称 (默认: arboris:sync:events)"
    echo "  --service-url  同步服务URL (默认: http://localhost:8080)"
    echo ""
    echo "示例:"
    echo "  $0"
    echo "  $0 --redis-host ************* --redis-port 6380"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --redis-host)
            REDIS_HOST="$2"
            shift 2
            ;;
        --redis-port)
            REDIS_PORT="$2"
            shift 2
            ;;
        --stream)
            STREAM_NAME="$2"
            shift 2
            ;;
        --service-url)
            SYNC_SERVICE_URL="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查依赖
if ! command -v redis-cli &> /dev/null; then
    log_error "redis-cli 未安装，请先安装Redis客户端"
    exit 1
fi

if ! command -v curl &> /dev/null; then
    log_error "curl 未安装，请先安装curl"
    exit 1
fi

# 运行主程序
main
