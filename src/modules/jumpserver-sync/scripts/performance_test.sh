#!/bin/bash

# JumpServer同步服务性能测试脚本

set -e

# 配置
REDIS_HOST="127.0.0.1"
REDIS_PORT="6379"
STREAM_NAME="arboris:sync:events"
SYNC_SERVICE_URL="http://localhost:8080"

# 测试参数
TOTAL_EVENTS=1000
BATCH_SIZE=50
CONCURRENT_PUBLISHERS=5

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_step "检查依赖..."
    
    if ! command -v redis-cli &> /dev/null; then
        log_error "redis-cli未安装"
        exit 1
    fi
    
    if ! command -v curl &> /dev/null; then
        log_error "curl未安装"
        exit 1
    fi
    
    if ! command -v bc &> /dev/null; then
        log_error "bc未安装，用于计算统计数据"
        exit 1
    fi
}

# 检查服务状态
check_services() {
    log_step "检查服务状态..."
    
    # 检查Redis
    if ! redis-cli -h $REDIS_HOST -p $REDIS_PORT ping > /dev/null 2>&1; then
        log_error "Redis服务不可用"
        exit 1
    fi
    
    # 检查同步服务
    if ! curl -s $SYNC_SERVICE_URL/health > /dev/null 2>&1; then
        log_error "JumpServer同步服务不可用"
        exit 1
    fi
    
    log_info "所有服务正常运行"
}

# 清理测试数据
cleanup_test_data() {
    log_step "清理测试数据..."
    
    # 删除测试流（可选）
    # redis-cli -h $REDIS_HOST -p $REDIS_PORT DEL "${STREAM_NAME}_test" > /dev/null 2>&1 || true
    
    log_info "测试数据清理完成"
}

# 发布测试事件
publish_test_events() {
    local publisher_id=$1
    local events_per_publisher=$2
    local start_time=$(date +%s%N)
    
    for ((i=1; i<=events_per_publisher; i++)); do
        local event_id="${publisher_id}_${i}_$(date +%s%N)"
        local timestamp=$(date +%s)
        
        redis-cli -h $REDIS_HOST -p $REDIS_PORT XADD $STREAM_NAME "*" \
            "id" "perf_test_$event_id" \
            "type" "node.create" \
            "source" "performance_test" \
            "timestamp" "$timestamp" \
            "data.id" "$((publisher_id * 1000 + i))" \
            "data.name" "性能测试节点_${publisher_id}_${i}" \
            "data.path" "/performance_test/publisher_${publisher_id}/node_${i}" \
            "data.cate" "test" \
            "metadata.test" "true" \
            "metadata.publisher_id" "$publisher_id" \
            "metadata.sequence" "$i" > /dev/null
        
        # 每批次后稍作停顿
        if ((i % BATCH_SIZE == 0)); then
            sleep 0.1
        fi
    done
    
    local end_time=$(date +%s%N)
    local duration=$(echo "scale=3; ($end_time - $start_time) / 1000000000" | bc)
    
    echo "Publisher $publisher_id: 发布 $events_per_publisher 个事件，耗时 ${duration}s"
}

# 运行性能测试
run_performance_test() {
    log_step "开始性能测试..."
    
    echo "测试参数:"
    echo "  总事件数: $TOTAL_EVENTS"
    echo "  并发发布者: $CONCURRENT_PUBLISHERS"
    echo "  批次大小: $BATCH_SIZE"
    echo ""
    
    # 记录初始状态
    local initial_stream_length=0
    if redis-cli -h $REDIS_HOST -p $REDIS_PORT EXISTS $STREAM_NAME | grep -q "1"; then
        initial_stream_length=$(redis-cli -h $REDIS_HOST -p $REDIS_PORT XLEN $STREAM_NAME)
    fi
    
    log_info "初始流长度: $initial_stream_length"
    
    # 计算每个发布者的事件数
    local events_per_publisher=$((TOTAL_EVENTS / CONCURRENT_PUBLISHERS))
    
    log_info "开始并发发布事件..."
    local test_start_time=$(date +%s%N)
    
    # 启动并发发布者
    local pids=()
    for ((p=1; p<=CONCURRENT_PUBLISHERS; p++)); do
        publish_test_events $p $events_per_publisher &
        pids+=($!)
    done
    
    # 等待所有发布者完成
    for pid in "${pids[@]}"; do
        wait $pid
    done
    
    local test_end_time=$(date +%s%N)
    local total_publish_duration=$(echo "scale=3; ($test_end_time - $test_start_time) / 1000000000" | bc)
    
    log_info "事件发布完成，总耗时: ${total_publish_duration}s"
    
    # 等待事件处理
    log_info "等待事件处理..."
    sleep 5
    
    # 检查最终状态
    local final_stream_length=$(redis-cli -h $REDIS_HOST -p $REDIS_PORT XLEN $STREAM_NAME)
    local processed_events=$((final_stream_length - initial_stream_length))
    
    echo ""
    echo "=== 性能测试结果 ==="
    echo "发布事件数: $TOTAL_EVENTS"
    echo "流中新增事件: $processed_events"
    echo "发布总耗时: ${total_publish_duration}s"
    echo "发布速率: $(echo "scale=2; $TOTAL_EVENTS / $total_publish_duration" | bc) events/s"
    echo ""
}

# 监控事件处理
monitor_event_processing() {
    log_step "监控事件处理..."
    
    local monitor_duration=60  # 监控60秒
    local check_interval=5     # 每5秒检查一次
    local checks=$((monitor_duration / check_interval))
    
    echo "监控事件处理情况 (${monitor_duration}秒)..."
    
    local prev_length=$(redis-cli -h $REDIS_HOST -p $REDIS_PORT XLEN $STREAM_NAME 2>/dev/null || echo "0")
    
    for ((i=1; i<=checks; i++)); do
        sleep $check_interval
        
        local current_length=$(redis-cli -h $REDIS_HOST -p $REDIS_PORT XLEN $STREAM_NAME 2>/dev/null || echo "0")
        local processed_in_interval=$((current_length - prev_length))
        local processing_rate=$(echo "scale=2; $processed_in_interval / $check_interval" | bc)
        
        echo "第${i}次检查: 流长度=$current_length, 新增=$processed_in_interval, 处理速率=${processing_rate} events/s"
        
        prev_length=$current_length
    done
}

# 获取服务指标
get_service_metrics() {
    log_step "获取服务指标..."
    
    echo ""
    echo "=== 服务指标 ==="
    
    # 获取服务信息
    if curl -s $SYNC_SERVICE_URL/info > /dev/null 2>&1; then
        echo "服务信息:"
        curl -s $SYNC_SERVICE_URL/info | jq '.' 2>/dev/null || echo "无法解析服务信息"
    fi
    
    # 获取Prometheus指标（如果可用）
    if curl -s $SYNC_SERVICE_URL:9090/metrics > /dev/null 2>&1; then
        echo ""
        echo "关键指标:"
        curl -s $SYNC_SERVICE_URL:9090/metrics | grep -E "(jumpserver_sync_events|process_)" | head -10
    fi
    
    echo ""
}

# 生成测试报告
generate_report() {
    log_step "生成测试报告..."
    
    local report_file="performance_test_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > $report_file << EOF
JumpServer同步服务性能测试报告
================================

测试时间: $(date)
测试参数:
  - 总事件数: $TOTAL_EVENTS
  - 并发发布者: $CONCURRENT_PUBLISHERS
  - 批次大小: $BATCH_SIZE

环境信息:
  - Redis: $REDIS_HOST:$REDIS_PORT
  - 同步服务: $SYNC_SERVICE_URL
  - 流名称: $STREAM_NAME

测试结果:
$(run_performance_test 2>&1)

服务指标:
$(get_service_metrics 2>&1)

EOF
    
    log_info "测试报告已生成: $report_file"
}

# 显示帮助
show_help() {
    echo "JumpServer同步服务性能测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help           显示帮助信息"
    echo "  -n, --events NUM     总事件数 (默认: 1000)"
    echo "  -c, --concurrent NUM 并发发布者数 (默认: 5)"
    echo "  -b, --batch NUM      批次大小 (默认: 50)"
    echo "  --monitor-only       仅监控，不发布事件"
    echo "  --cleanup            清理测试数据"
    echo ""
    echo "示例:"
    echo "  $0                           # 使用默认参数运行测试"
    echo "  $0 -n 5000 -c 10 -b 100     # 自定义参数测试"
    echo "  $0 --monitor-only            # 仅监控事件处理"
}

# 主函数
main() {
    echo "=== JumpServer同步服务性能测试 ==="
    echo ""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -n|--events)
                TOTAL_EVENTS="$2"
                shift 2
                ;;
            -c|--concurrent)
                CONCURRENT_PUBLISHERS="$2"
                shift 2
                ;;
            -b|--batch)
                BATCH_SIZE="$2"
                shift 2
                ;;
            --monitor-only)
                monitor_event_processing
                exit 0
                ;;
            --cleanup)
                cleanup_test_data
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 执行测试
    check_dependencies
    check_services
    run_performance_test
    monitor_event_processing
    get_service_metrics
    cleanup_test_data
    
    log_info "性能测试完成！"
}

# 运行主函数
main "$@"
