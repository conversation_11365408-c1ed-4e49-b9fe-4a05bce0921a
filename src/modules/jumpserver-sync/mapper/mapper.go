package mapper

import (
	"fmt"
	"regexp"
	"strings"

	"arboris/src/modules/jumpserver-sync/config"
	"arboris/src/modules/jumpserver-sync/events"
	"arboris/src/modules/jumpserver-sync/jumpserver"
)

// Mapper 数据映射器
type Mapper struct {
	config *config.ConfigT
}

// NewMapper 创建新的映射器
func NewMapper(cfg *config.ConfigT) *Mapper {
	return &Mapper{
		config: cfg,
	}
}

// ShouldSyncNode 检查节点是否应该同步
func (m *Mapper) ShouldSyncNode(nodeData *events.NodeEventData) bool {
	// 检查包含路径
	if len(m.config.Sync.Rules.IncludePaths) > 0 {
		included := false
		for _, includePath := range m.config.Sync.Rules.IncludePaths {
			if strings.HasPrefix(nodeData.Path, includePath) {
				included = true
				break
			}
		}
		if !included {
			return false
		}
	}

	// 检查排除路径
	for _, excludePath := range m.config.Sync.Rules.ExcludePaths {
		if strings.HasPrefix(nodeData.Path, excludePath) {
			return false
		}
	}

	// 检查过滤器
	for key, value := range m.config.Sync.Rules.Filters {
		switch key {
		case "cate":
			if nodeData.Cate != value {
				return false
			}
		case "leaf":
			if fmt.Sprintf("%d", nodeData.Leaf) != value {
				return false
			}
		}
	}

	return true
}

// ShouldSyncHost 检查主机是否应该同步
func (m *Mapper) ShouldSyncHost(hostData *events.HostEventData) bool {
	// 检查过滤器
	for key, value := range m.config.Sync.Rules.Filters {
		switch key {
		case "cate":
			if hostData.Cate != value {
				return false
			}
		case "tenant":
			if hostData.Tenant != value {
				return false
			}
		}
	}

	return true
}

// MapNodeToJumpServer 将RDB节点映射为JumpServer节点
func (m *Mapper) MapNodeToJumpServer(nodeData *events.NodeEventData) (*jumpserver.Node, error) {
	// 应用节点映射规则
	key := m.applyNodeRules(nodeData.Path)

	// 构建JumpServer节点
	jsNode := &jumpserver.Node{
		Key:      key,
		Value:    nodeData.Name,
		Parent:   m.getParentKey(key),
		FullPath: key,
		Meta: map[string]string{
			"rdb_id":   fmt.Sprintf("%d", nodeData.ID),
			"rdb_path": nodeData.Path,
			"cate":     nodeData.Cate,
			"note":     nodeData.Note,
		},
	}

	return jsNode, nil
}

// MapHostToJumpServer 将AMS主机映射为JumpServer资产
func (m *Mapper) MapHostToJumpServer(hostData *events.HostEventData) (*jumpserver.Asset, error) {
	// 应用机器映射规则
	platform, protocols := m.applyMachineRules(hostData)

	// 构建JumpServer资产
	jsAsset := &jumpserver.Asset{
		Hostname:  m.getHostname(hostData),
		IP:        hostData.IP,
		Platform:  platform,
		Protocols: protocols,
		IsActive:  true,
		Comment:   hostData.Note,
		Labels: map[string]string{
			"ams_id":       fmt.Sprintf("%d", hostData.ID),
			"sn":           hostData.SN,
			"ident":        hostData.Ident,
			"cate":         hostData.Cate,
			"tenant":       hostData.Tenant,
			"zone":         hostData.Zone,
			"rack":         hostData.Rack,
			"idc":          hostData.IDC,
			"cpu":          hostData.CPU,
			"mem":          hostData.Mem,
			"disk":         hostData.Disk,
			"gpu":          hostData.GPU,
			"model":        hostData.Model,
			"manufacturer": hostData.Manufacturer,
		},
	}

	return jsAsset, nil
}

// applyNodeRules 应用节点映射规则
func (m *Mapper) applyNodeRules(path string) string {
	// RDB的node path格式: inner.code.master
	// JumpServer需要的格式: /inner/code/master

	// 首先应用配置的映射规则
	mappedPath := path
	for _, rule := range m.config.Sync.Mapping.NodeRules {
		if matched, _ := regexp.MatchString(rule.Pattern, mappedPath); matched {
			// 应用转换规则
			if rule.TargetPrefix != "" {
				// 使用正则替换
				re := regexp.MustCompile(rule.Pattern)
				mappedPath = re.ReplaceAllString(mappedPath, rule.TargetPrefix)
				break // 只应用第一个匹配的规则
			}
		}
	}

	// 然后转换为JumpServer路径格式
	return convertToJumpServerPath(mappedPath)
}

// convertToJumpServerPath 将点分隔路径转换为JumpServer的斜杠分隔路径
func convertToJumpServerPath(rdbPath string) string {
	if rdbPath == "" {
		return "/"
	}

	// inner.code.master -> /inner/code/master
	parts := strings.Split(rdbPath, ".")
	return "/" + strings.Join(parts, "/")
}

// applyMachineRules 应用机器映射规则
func (m *Mapper) applyMachineRules(hostData *events.HostEventData) (string, []jumpserver.Protocol) {
	platform := "Linux" // 默认平台
	protocols := []jumpserver.Protocol{
		{Name: "ssh", Port: 22},
	}

	for _, rule := range m.config.Sync.Mapping.MachineRules {
		if m.matchCondition(rule.Condition, hostData) {
			if rule.Platform != "" {
				platform = rule.Platform
			}
			if len(rule.Protocols) > 0 {
				protocols = make([]jumpserver.Protocol, len(rule.Protocols))
				for i, p := range rule.Protocols {
					protocols[i] = jumpserver.Protocol{
						Name: p.Name,
						Port: p.Port,
					}
				}
			}
			break
		}
	}

	return platform, protocols
}

// matchCondition 匹配条件
func (m *Mapper) matchCondition(condition string, hostData *events.HostEventData) bool {
	// 简单的条件匹配实现
	// 支持格式: field=value 或 field~pattern
	parts := strings.SplitN(condition, "=", 2)
	if len(parts) == 2 {
		field, value := parts[0], parts[1]
		return m.getFieldValue(field, hostData) == value
	}

	parts = strings.SplitN(condition, "~", 2)
	if len(parts) == 2 {
		field, pattern := parts[0], parts[1]
		matched, _ := regexp.MatchString(pattern, m.getFieldValue(field, hostData))
		return matched
	}

	return false
}

// getFieldValue 获取字段值
func (m *Mapper) getFieldValue(field string, hostData *events.HostEventData) string {
	switch field {
	case "cate":
		return hostData.Cate
	case "tenant":
		return hostData.Tenant
	case "zone":
		return hostData.Zone
	case "os_version":
		return hostData.OSVersion
	case "cpu_model":
		return hostData.CPUModel
	case "manufacturer":
		return hostData.Manufacturer
	default:
		return ""
	}
}

// getParentKey 获取父节点key
func (m *Mapper) getParentKey(key string) string {
	parts := strings.Split(key, "/")
	if len(parts) <= 1 {
		return ""
	}
	return strings.Join(parts[:len(parts)-1], "/")
}

// getHostname 获取主机名
func (m *Mapper) getHostname(hostData *events.HostEventData) string {
	if hostData.Name != "" {
		return hostData.Name
	}
	if hostData.Ident != "" {
		return hostData.Ident
	}
	return hostData.IP
}
