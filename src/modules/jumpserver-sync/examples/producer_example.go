package main

import (
	"fmt"
	"log"
	"time"

	"arboris/src/modules/jumpserver-sync/events"
)

// 这是一个示例程序，展示如何在RDB和AMS模块中集成事件生产者

func main() {
	// 创建事件生产者
	producer, err := events.NewProducer(
		"127.0.0.1:6379", // Redis地址
		"",               // Redis密码
		0,                // Redis数据库
		"arboris:sync:events", // Stream名称
	)
	if err != nil {
		log.Fatalf("Failed to create producer: %v", err)
	}
	defer producer.Close()

	// 示例1: 发布节点创建事件
	fmt.Println("Publishing node create event...")
	nodeData := &events.NodeEventData{
		ID:          1,
		PID:         0,
		Ident:       "company",
		Name:        "公司",
		Note:        "公司根节点",
		Path:        "/公司",
		Leaf:        0,
		Cate:        "node",
		IconColor:   "#1890ff",
		IconChar:    "🏢",
		Proxy:       0,
		Creator:     "admin",
		LastUpdated: time.Now().Unix(),
	}

	if err := producer.PublishNodeEvent(events.EventNodeCreate, nodeData); err != nil {
		logger.Infof("Failed to publish node event: %v", err)
	} else {
		fmt.Println("Node create event published successfully")
	}

	// 示例2: 发布主机创建事件
	fmt.Println("Publishing host create event...")
	hostData := &events.HostEventData{
		ID:            1,
		SN:            "SN001",
		IP:            "*************",
		Ident:         "web-server-01",
		Name:          "Web服务器01",
		OSVersion:     "Ubuntu 20.04",
		KernelVersion: "5.4.0-74-generic",
		CPUModel:      "Intel Xeon E5-2680",
		CPU:           "32核",
		Mem:           "128GB",
		Disk:          "2TB SSD",
		Note:          "生产环境Web服务器",
		Cate:          "server",
		Tenant:        "default",
		Clock:         time.Now().Unix(),
		GPU:           "2",
		GPUModel:      "NVIDIA Tesla V100",
		Model:         "Dell PowerEdge R740",
		IDC:           "IDC-Beijing",
		Zone:          "zone-prod",
		Rack:          "rack-01",
		Manufacturer:  "Dell",
	}

	if err := producer.PublishHostEvent(events.EventHostCreate, hostData); err != nil {
		logger.Infof("Failed to publish host event: %v", err)
	} else {
		fmt.Println("Host create event published successfully")
	}

	// 示例3: 发布节点更新事件
	fmt.Println("Publishing node update event...")
	nodeData.Name = "公司总部"
	nodeData.Note = "更新后的公司根节点"
	nodeData.LastUpdated = time.Now().Unix()

	if err := producer.PublishNodeEvent(events.EventNodeUpdate, nodeData); err != nil {
		logger.Infof("Failed to publish node update event: %v", err)
	} else {
		fmt.Println("Node update event published successfully")
	}

	// 示例4: 发布主机更新事件
	fmt.Println("Publishing host update event...")
	hostData.Name = "Web服务器01-更新"
	hostData.Note = "更新后的Web服务器"
	hostData.Clock = time.Now().Unix()

	if err := producer.PublishHostEvent(events.EventHostUpdate, hostData); err != nil {
		logger.Infof("Failed to publish host update event: %v", err)
	} else {
		fmt.Println("Host update event published successfully")
	}

	// 示例5: 发布资源绑定事件
	fmt.Println("Publishing resource bind event...")
	bindData := &events.ResourceBindEventData{
		NodeID:       1,
		ResourceID:   1,
		NodePath:     "/公司/IT部门",
		ResourceUUID: "host-uuid-001",
	}

	if err := producer.PublishResourceBindEvent(events.EventResourceBind, bindData); err != nil {
		logger.Infof("Failed to publish resource bind event: %v", err)
	} else {
		fmt.Println("Resource bind event published successfully")
	}

	// 获取流信息
	fmt.Println("\nGetting stream info...")
	streamInfo, err := producer.GetStreamInfo()
	if err != nil {
		logger.Infof("Failed to get stream info: %v", err)
	} else {
		fmt.Printf("Stream length: %d\n", streamInfo.Length)
		fmt.Printf("Stream first entry ID: %s\n", streamInfo.FirstEntry.ID)
		fmt.Printf("Stream last entry ID: %s\n", streamInfo.LastEntry.ID)
	}

	fmt.Println("\nAll events published successfully!")
}

// 在实际的RDB模块中，你可以这样集成事件发布：
/*
// 在RDB模块的节点操作函数中
func NodeCreate(node *Node) error {
    // 创建节点的业务逻辑
    if err := createNodeInDB(node); err != nil {
        return err
    }
    
    // 发布事件
    if producer != nil {
        nodeData := &events.NodeEventData{
            ID:          node.ID,
            PID:         node.PID,
            Ident:       node.Ident,
            Name:        node.Name,
            Note:        node.Note,
            Path:        node.Path,
            Leaf:        node.Leaf,
            Cate:        node.Cate,
            IconColor:   node.IconColor,
            IconChar:    node.IconChar,
            Proxy:       node.Proxy,
            Creator:     node.Creator,
            LastUpdated: time.Now().Unix(),
        }
        
        if err := producer.PublishNodeEvent(events.EventNodeCreate, nodeData); err != nil {
            logger.Infof("Failed to publish node create event: %v", err)
            // 注意：这里不应该返回错误，避免影响主业务流程
        }
    }
    
    return nil
}
*/

// 在实际的AMS模块中，你可以这样集成事件发布：
/*
// 在AMS模块的主机操作函数中
func HostCreate(host *Host) error {
    // 创建主机的业务逻辑
    if err := createHostInDB(host); err != nil {
        return err
    }
    
    // 发布事件
    if producer != nil {
        hostData := &events.HostEventData{
            ID:            host.ID,
            SN:            host.SN,
            IP:            host.IP,
            Ident:         host.Ident,
            Name:          host.Name,
            OSVersion:     host.OSVersion,
            KernelVersion: host.KernelVersion,
            CPUModel:      host.CPUModel,
            CPU:           host.CPU,
            Mem:           host.Mem,
            Disk:          host.Disk,
            Note:          host.Note,
            Cate:          host.Cate,
            Tenant:        host.Tenant,
            Clock:         time.Now().Unix(),
            GPU:           host.GPU,
            GPUModel:      host.GPUModel,
            Model:         host.Model,
            IDC:           host.IDC,
            Zone:          host.Zone,
            Rack:          host.Rack,
            Manufacturer:  host.Manufacturer,
        }
        
        if err := producer.PublishHostEvent(events.EventHostCreate, hostData); err != nil {
            logger.Infof("Failed to publish host create event: %v", err)
            // 注意：这里不应该返回错误，避免影响主业务流程
        }
    }
    
    return nil
}
*/
