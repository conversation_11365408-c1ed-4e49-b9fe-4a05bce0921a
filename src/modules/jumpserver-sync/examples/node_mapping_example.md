# 节点路径映射示例

## 🎯 RDB节点路径格式

RDB系统中的节点路径格式为: `inner.code.master`

由于路径已经是英文格式，通常情况下**无需进行路径转换**，直接使用相同的路径即可。

## 📋 实际路径示例

### RDB节点路径格式
```
inner.code.master
inner.ops.production
inner.ops.testing
inner.dev.frontend
inner.dev.backend
inner.data.mysql
inner.data.redis
```

### 默认映射（无转换）
```yaml
sync:
  mapping:
    node_rules: []  # 空规则，直接使用原路径
```

**映射结果:**
```
RDB路径                    →    JumpServer路径
inner.code.master         →    inner.code.master
inner.ops.production      →    inner.ops.production
inner.ops.testing         →    inner.ops.testing
inner.dev.frontend        →    inner.dev.frontend
inner.dev.backend         →    inner.dev.backend
```

## 🔄 实际应用示例

### 示例1: 权限授权场景

**RDB操作:**
```
管理员给用户 "zhangsan" 授权节点 "inner.ops.production" 的读写权限
```

**事件数据:**
```json
{
  "event_type": "permission.grant",
  "node_path": "inner.ops.production",
  "user_ids": [123],
  "permission": "rw"
}
```

**JumpServer同步结果:**
```
创建资产权限:
- 用户: zhangsan
- 资产节点: inner.ops.production
- 权限动作: connect, upload, download
```

### 示例2: 团队权限授权

**RDB操作:**
```
给团队 "ops-team" 授权节点 "inner.data.mysql" 的只读权限
```

**事件数据:**
```json
{
  "event_type": "permission.grant",
  "node_path": "inner.data.mysql",
  "team_ids": [456],
  "permission": "r"
}
```

**JumpServer同步结果:**
```
创建资产权限:
- 用户组: ops-team
- 资产节点: inner.data.mysql
- 权限动作: connect
```

### 示例3: 多层级节点权限

**RDB节点结构:**
```
inner.ops.production
├── inner.ops.production.web
├── inner.ops.production.db
└── inner.ops.production.cache
```

**权限授权:**
```
给用户授权 "inner.ops.production" 节点权限
```

**JumpServer同步:**
```
查询该节点下的所有资产:
- inner.ops.production.web 下的所有主机
- inner.ops.production.db 下的所有主机
- inner.ops.production.cache 下的所有主机

为用户创建对应的资产权限
```

## 🎯 实际应用场景

### 场景1: 权限授权

当管理员在RDB中给用户授权节点时：

1. **RDB操作:**
   ```
   给用户 "张三" 授权节点 "/公司/技术部/后端组" 的读写权限
   ```

2. **事件发布:**
   ```json
   {
     "event_type": "permission.grant",
     "node_path": "/公司/技术部/后端组",
     "user_ids": [123],
     "permission": "rw"
   }
   ```

3. **路径映射:**
   ```
   原始路径: /公司/技术部/后端组
   映射后:   /Company/技术部/后端组
   ```

4. **JumpServer同步:**
   ```
   创建资产权限:
   - 用户: 张三
   - 资产节点: /Company/技术部/后端组
   - 权限: connect, upload, download
   ```

### 场景2: 资产查询

当同步服务需要查询节点下的资产时：

1. **接收事件:**
   ```
   节点路径: /生产环境/Web服务器
   ```

2. **路径映射:**
   ```
   映射后: /Production/Web服务器
   ```

3. **资产查询:**
   ```sql
   SELECT ident FROM host 
   WHERE node_path = '/Production/Web服务器' 
      OR node_path LIKE '/Production/Web服务器/%'
   ```

4. **JumpServer资产:**
   ```
   找到资产: nginx-prod-01, nginx-prod-02, apache-prod-01
   ```

## ⚙️ 映射规则配置说明

### 规则格式

```yaml
node_rules:
  - pattern: "正则表达式模式"
    target_prefix: "目标前缀"
    description: "规则描述"
```

### 参数说明

- **pattern**: 正则表达式，用于匹配RDB节点路径
  - `^` 表示路径开头
  - `/公司/` 表示匹配以"/公司/"开头的路径
  
- **target_prefix**: 替换后的前缀
  - 将匹配的部分替换为这个前缀
  
- **description**: 规则描述，用于文档和调试

### 匹配优先级

规则按配置顺序匹配，第一个匹配的规则生效：

```yaml
node_rules:
  # 优先级1: 更具体的规则
  - pattern: "^/公司/技术部/"
    target_prefix: "/Company/Tech/"
  
  # 优先级2: 更通用的规则
  - pattern: "^/公司/"
    target_prefix: "/Company/"
```

## 🔧 调试和验证

### 查看映射日志

```bash
# 查看映射处理日志
tail -f logs/jumpserver-sync.log | grep "node mapping"
```

### 测试映射规则

```bash
# 可以在代码中添加测试函数
func testNodeMapping() {
    testCases := []struct{
        input    string
        expected string
    }{
        {"/公司/技术部/后端组", "/Company/技术部/后端组"},
        {"/生产环境/Web服务器", "/Production/Web服务器"},
        {"/北京/机房A/服务器", "/Beijing/机房A/服务器"},
    }
    
    for _, tc := range testCases {
        result := applyNodeMapping(tc.input)
        fmt.Printf("Input: %s -> Output: %s (Expected: %s)\n", 
                   tc.input, result, tc.expected)
    }
}
```

这样的映射规则让您可以灵活地将RDB中的中文路径、业务路径转换为JumpServer中的标准化英文路径，便于管理和国际化。
