# 同名节点同步问题修复

## 🐛 问题描述

在 JumpServer 同步过程中，当不同层级存在同名节点时，会出现无法正确同步的问题。

### 问题场景

假设有以下节点结构：
```
公司
├── 部门A
│   ├── 开发组
│   └── 测试组
└── 部门B
    ├── 开发组  ← 同名节点
    └── 测试组  ← 同名节点
```

在原有逻辑中，当同步"部门B"下的"开发组"时，系统会：
1. 检查是否存在名为"开发组"的节点
2. 发现"部门A"下已经有"开发组"节点
3. 误认为节点已存在，跳过创建
4. 导致"部门B"下的"开发组"无法同步

## 🔍 根本原因分析

### 原有逻辑的问题

在 `handleNodeCreate` 方法中：

```go
// 原有的错误逻辑
existingNode, err := h.jsClient.GetNodeByValue(jsNode.Value)
if existingNode != nil {
    // 仅通过节点名称判断存在性，忽略了路径层级
    logger.Infof("Node already exists, skipping creation: node_key=%s", jsNode.Key)
    return nil
}
```

`GetNodeByValue` 方法只通过节点的 `value`（显示名称）进行查找：
```go
func (c *Client) GetNodeByValue(value string) (*Node, error) {
    url := fmt.Sprintf("%s/api/v1/assets/nodes/?value=%s", c.baseURL, url.QueryEscape(value))
    // 只查找名称匹配的节点，不考虑路径
}
```

### 问题影响

1. **节点丢失**：同名节点无法正确创建
2. **权限错误**：权限可能绑定到错误的节点
3. **结构混乱**：组织架构无法正确反映到 JumpServer

## ✅ 修复方案

### 1. 正确理解 JumpServer API

经过分析发现，JumpServer 的 `/api/v1/assets/nodes/` 接口**没有** `full_path` 参数，实际支持的查询参数是：
- `value` - 节点名称
- 通过父子关系组织节点

### 2. 新增 GetNodeByParentAndValue 方法

创建正确的节点查找方法，通过父节点ID和节点名称来确保唯一性：

```go
// GetNodeByParentAndValue 根据父节点ID和节点名称获取节点
func (c *Client) GetNodeByParentAndValue(parentID, value string) (*Node, error) {
    var apiURL string
    if parentID == "" {
        // 查找根级节点
        apiURL = fmt.Sprintf("%s/api/v1/assets/nodes/?value=%s", c.baseURL, url.QueryEscape(value))
    } else {
        // 查找指定父节点下的子节点
        apiURL = fmt.Sprintf("%s/api/v1/assets/nodes/%s/children/?value=%s", c.baseURL, parentID, url.QueryEscape(value))
    }
    // ... 查询逻辑 ...
}
```

### 3. 修改节点存在性检查逻辑

使用父节点ID + 节点名称的组合来检查唯一性：

```go
// 检查节点是否已存在 - 使用父节点ID和节点名称来确保唯一性
var parentID string
if nodeData.GetNodePID() != 0 {
    // 获取并确保父节点存在
    // ...
    parentID = parentJSNode.ID
}

existingNode, err := h.jsClient.GetNodeByParentAndValue(parentID, jsNode.Value)
if err != nil {
    return fmt.Errorf("failed to check existing node: %v", err)
}

if existingNode != nil {
    logger.Infof("Node already exists under parent, skipping creation: parent_id=%s, value=%s",
        parentID, jsNode.Value)
    return nil
}
```

### 4. 更新节点更新逻辑

在 `handleNodeUpdate` 中也使用父节点ID + 节点名称查找：

```go
// 获取父节点ID用于查找
var parentID string
if nodeData.GetNodePID() != 0 {
    parentNodeData, err := h.getNodeByID(nodeData.GetNodePID())
    if err != nil {
        return fmt.Errorf("failed to get parent node: %v", err)
    }

    if parentJSNode, exists := h.nodeCache[parentNodeData.Path]; exists {
        parentID = parentJSNode.ID
    }
}

// 通过父节点ID和节点名称查找节点，避免同名节点冲突
existingNode, err = h.jsClient.GetNodeByParentAndValue(parentID, jsNode.Value)
```

### 5. 修复权限处理中的节点查找

在权限同步中也使用父节点ID + 节点名称查找：

```go
func (h *Handler) convertNodePathToJSNodePK(nodePath string) (string, string) {
    // 获取父节点信息用于精确查找
    var parentID string
    if len(parts) > 1 {
        parentPath := strings.Join(parts[:len(parts)-1], ".")
        // 获取父节点ID...
    }

    // 通过父节点ID和节点名称查找JumpServer中的对应节点
    node, err := h.jsClient.GetNodeByParentAndValue(parentID, nodeName)
    if err != nil {
        return "", ""
    }

    return node.ID, nodeName
}
```

## 🧪 测试验证

创建了专门的测试用例 `same_name_node_test.go` 来验证修复效果：

### 测试场景

1. **路径唯一性测试**：验证不同路径的同名节点可以共存
2. **节点创建测试**：模拟同名节点的创建过程
3. **路径转换测试**：验证 RDB 路径到 JumpServer 路径的转换

### 运行测试

```bash
cd src/modules/jumpserver-sync
go test -v ./test -run TestSameNameNode
```

## 📊 修复效果

### 修复前
```
部门A/开发组 ✅ 创建成功
部门B/开发组 ❌ 跳过创建（误认为已存在）
```

### 修复后
```
部门A/开发组 ✅ 创建成功（路径：/公司/部门A/开发组）
部门B/开发组 ✅ 创建成功（路径：/公司/部门B/开发组）
```

## 🔄 向后兼容性

此修复保持了向后兼容性：

1. **API 接口不变**：仍使用相同的 JumpServer API
2. **配置不变**：无需修改现有配置文件
3. **数据结构不变**：节点数据结构保持一致

## 📝 注意事项

1. **路径格式**：确保 RDB 路径正确转换为 JumpServer 路径格式
2. **权限同步**：权限同步也会受益于此修复
3. **日志监控**：注意观察同步日志，确认节点正确创建

## 🚀 部署建议

1. **测试环境验证**：先在测试环境验证修复效果
2. **备份数据**：部署前备份 JumpServer 数据
3. **监控同步**：部署后密切监控同步日志
4. **验证结果**：检查 JumpServer 中的节点结构是否正确

## 📋 相关文件

- `src/modules/jumpserver-sync/sync/handler.go` - 主要修复逻辑
- `src/modules/jumpserver-sync/jumpserver/client.go` - 新增 GetNodeByParentAndValue 方法
- `src/modules/jumpserver-sync/sync/handler_permission.go` - 权限处理修复
- `src/modules/jumpserver-sync/test/fix_validation_test.go` - 测试用例
