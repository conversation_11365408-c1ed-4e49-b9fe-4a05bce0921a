package jumpserver

// Node JumpServer节点结构
type Node struct {
	ID       string            `json:"id"`
	Key      string            `json:"key"`
	Value    string            `json:"value"`
	Parent   string            `json:"parent"`
	Meta     map[string]string `json:"meta"`
	FullPath string            `json:"full_path"`
}

// Asset JumpServer资产结构
type Asset struct {
	ID        string            `json:"id"`
	Hostname  string            `json:"hostname"`
	IP        string            `json:"ip"`
	Address   string            `json:"address"`   // 新增address字段，与hosts API保持一致
	Name      string            `json:"name"`      // 新增name字段，与hosts API保持一致
	Platform  string            `json:"platform"`
	Protocols []Protocol        `json:"protocols"`
	IsActive  bool              `json:"is_active"`
	Comment   string            `json:"comment"`
	Labels    map[string]string `json:"labels"`
	Nodes     []string          `json:"nodes"`
}

// Protocol 协议配置
type Protocol struct {
	Name string `json:"name"`
	Port int    `json:"port"`
}

// AuthInfo 认证信息
type AuthInfo struct {
	Username string `json:"username"`
	Password string `json:"password"`
	Token    string `json:"token"`
}

// APIResponse JumpServer API响应
type APIResponse struct {
	Count    int         `json:"count"`
	Next     string      `json:"next"`
	Previous string      `json:"previous"`
	Results  interface{} `json:"results"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Detail string            `json:"detail"`
	Errors map[string]string `json:"errors"`
}

// SourceInfo JumpServer source字段结构
type SourceInfo struct {
	Value string `json:"value"`
	Label string `json:"label"`
}

// RoleInfo JumpServer角色信息结构
type RoleInfo struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	DisplayName string `json:"display_name"`
}

// User JumpServer用户结构
type User struct {
	ID          string        `json:"id"`
	Username    string        `json:"username"`
	Name        string        `json:"name"`
	Email       string        `json:"email"`
	Phone       interface{}   `json:"phone"`       // 可能是null或字符串
	Password    string        `json:"password,omitempty"`
	IsActive    bool          `json:"is_active"`
	IsSuperuser bool          `json:"is_superuser"`
	DateJoined  string        `json:"date_joined"`
	LastLogin   interface{}   `json:"last_login"` // 可能是null或字符串
	Comment     interface{}   `json:"comment"`    // 可能是null或字符串
	Source      interface{}   `json:"source"`     // 可能是字符串或对象
	Groups      []interface{} `json:"groups"`     // 可能是字符串数组或对象数组
	UserGroups  []interface{} `json:"user_groups"`
	SystemRoles []interface{} `json:"system_roles"` // 角色对象数组
	OrgRoles    []interface{} `json:"org_roles"`    // 角色对象数组
}

// UserGroup JumpServer用户组结构
type UserGroup struct {
	ID          string        `json:"id"`
	Name        string        `json:"name"`
	Comment     string        `json:"comment"`
	Users       []interface{} `json:"users"`       // 支持字符串数组或对象数组
	Labels      []interface{} `json:"labels"`      // 支持字符串数组或对象数组
	OrgID       string        `json:"org_id"`
	OrgName     string        `json:"org_name"`
	DateCreated interface{}   `json:"date_created"` // 支持多种时间格式
	CreatedBy   string        `json:"created_by"`
	UsersAmount int           `json:"users_amount"` // 用户数量字段
}

// GetUsernames 获取用户组中的用户名列表
func (ug *UserGroup) GetUsernames() []string {
	var usernames []string

	for _, user := range ug.Users {
		switch u := user.(type) {
		case string:
			// 如果是字符串，直接添加
			usernames = append(usernames, u)
		case map[string]interface{}:
			// 如果是对象，提取username字段
			if username, ok := u["username"].(string); ok {
				usernames = append(usernames, username)
			}
		}
	}

	return usernames
}

// SetUsernames 设置用户组的用户名列表（用于创建/更新请求）
func (ug *UserGroup) SetUsernames(usernames []string) {
	ug.Users = make([]interface{}, len(usernames))
	for i, username := range usernames {
		ug.Users[i] = username
	}
}

// User2GroupRelation 用户组关系结构
type User2GroupRelation struct {
	ID               int    `json:"id"`
	User             string `json:"user"`
	UserDisplay      string `json:"user_display"`
	UserGroup        string `json:"usergroup"`
	UserGroupDisplay string `json:"usergroup_display"`
}

// AssetPermission JumpServer资产权限结构
type AssetPermission struct {
	ID          string                   `json:"id"`
	Name        string                   `json:"name"`
	Users       []map[string]string      `json:"users"`       // [{"pk": "user_id"}]
	UserGroups  []map[string]string      `json:"user_groups"` // [{"pk": "group_id"}]
	Assets      []map[string]string      `json:"assets"`      // [{"pk": "asset_id"}]
	Nodes       []map[string]string      `json:"nodes"`       // [{"pk": "node_id"}]
	Accounts    []string                 `json:"accounts"`
	Protocols   []string                 `json:"protocols"`
	Actions     []map[string]interface{} `json:"actions"`
	IsActive    bool                     `json:"is_active"`
	DateStart   string                   `json:"date_start"`
	DateExpired string                   `json:"date_expired"`
	Comment     string                   `json:"comment"`
}

// UserAsset 用户资产结构（用于权限查询）
type UserAsset struct {
	ID       string `json:"id"`
	Hostname string `json:"hostname"`
	IP       string `json:"ip"`
	Address  string `json:"address"`
	Name     string `json:"name"`
	Platform string `json:"platform"`
	Comment  string `json:"comment"`
}

// PhoneInfo 电话信息结构
type PhoneInfo struct {
	Code  string `json:"code"`
	Phone int64  `json:"phone"`
}

// UserCreateRequest 用户创建请求
type UserCreateRequest struct {
	Username string     `json:"username"`
	Name     string     `json:"name"`
	Email    string     `json:"email"`
	Phone    *PhoneInfo `json:"phone"`
	IsActive bool       `json:"is_active"`
	Password string     `json:"password,omitempty"`
}

// UserCreateResponse 用户创建响应
type UserCreateResponse struct {
	User     *User  `json:"user"`
	Password string `json:"password"`
}
