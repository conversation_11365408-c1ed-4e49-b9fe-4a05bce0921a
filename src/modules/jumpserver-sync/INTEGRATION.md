# JumpServer同步服务集成指南

本文档说明如何在Arboris项目中集成JumpServer同步功能。

## 概述

JumpServer同步服务通过Redis Streams实现RDB树结构和资源绑定关系到JumpServer的实时同步。当RDB中的节点或资源绑定关系发生变化时，会自动发布事件到Redis Streams，JumpServer同步服务消费这些事件并同步到JumpServer。

## 架构图

```
┌─────────────┐    Events    ┌─────────────────┐    Sync    ┌─────────────┐
│ RDB Module  │─────────────→│  Redis Streams  │───────────→│ JumpServer  │
│             │              │                 │            │             │
└─────────────┘              └─────────────────┘            └─────────────┘
                                      │
                                      ▼
                              ┌─────────────────┐
                              │ Sync Service    │
                              │ - Event Handler │
                              │ - Data Mapper   │
                              │ - JS Client     │
                              └─────────────────┘
```

## 已集成的功能

### 1. RDB模块事件发布

已在以下位置集成了事件发布：

#### 节点事件
- **节点创建**: `src/modules/rdb/http/router_node.go` - `nodePost()`
- **节点更新**: `src/modules/rdb/http/router_node.go` - `nodePut()`
- **节点删除**: `src/modules/rdb/http/router_node.go` - `nodeDel()`

#### 资源绑定事件
- **资源绑定**: `src/modules/rdb/http/router_resource.go` - `resourceBindNode()`
- **资源解绑**: `src/modules/rdb/http/router_resource.go` - `resourceUnbindNode()`

### 2. 事件类型

支持以下事件类型：

```go
// 节点事件
events.EventNodeCreate   // 节点创建
events.EventNodeUpdate   // 节点更新
events.EventNodeDelete   // 节点删除

// 资源绑定事件
events.EventResourceBind   // 资源绑定到节点
events.EventResourceUnbind // 资源从节点解绑
```

### 3. 配置

RDB模块配置文件 `etc/rdb.yml` 已添加：

```yaml
# Redis配置 - 用于事件发布
redis:
  addr: "127.0.0.1:6379"
  password: ""
  db: 0

# 事件配置 - JumpServer同步
events:
  enable: true
  stream_name: "arboris:sync:events"
```

## 部署步骤

### 1. 启动Redis

确保Redis服务正在运行：

```bash
redis-server
```

### 2. 配置JumpServer同步服务

编辑 `src/modules/jumpserver-sync/etc/jumpserver-sync.yml`：

```yaml
# 数据库配置
mysql:
  rdb:
    dsn: "root:password@tcp(127.0.0.1:3306)/arboris_rdb"
  ams:
    dsn: "root:password@tcp(127.0.0.1:3306)/arboris_ams"

# Redis配置
redis:
  addr: "127.0.0.1:6379"
  password: ""
  db: 0

# JumpServer配置
jumpserver:
  base_url: "http://jumpserver.example.com"
  username: "admin"
  password: "admin"
  organization: "Default"

# 同步配置
sync:
  consumer:
    stream: "arboris:sync:events"
    group: "jumpserver-sync"
    consumer: "jumpserver-sync-001"
```

### 3. 启动JumpServer同步服务

```bash
cd src/modules/jumpserver-sync
make build
make run
```

### 4. 启动RDB模块

确保RDB模块配置了Redis和Events：

```bash
cd src/modules/rdb
go run rdb.go -f ../../etc/rdb.yml
```

## 验证同步

### 1. 检查服务状态

```bash
# 检查JumpServer同步服务健康状态
curl http://localhost:8080/health

# 检查服务信息
curl http://localhost:8080/info
```

### 2. 测试节点同步

在RDB中创建一个节点，观察JumpServer同步服务日志：

```bash
# 查看同步服务日志
tail -f src/modules/jumpserver-sync/logs/jumpserver-sync.log
```

### 3. 测试资源绑定同步

在RDB中将资源绑定到节点，观察同步效果。

## 监控和故障排查

### 1. 日志查看

```bash
# RDB模块日志
tail -f logs/rdb/rdb.log

# JumpServer同步服务日志
tail -f src/modules/jumpserver-sync/logs/jumpserver-sync.log
```

### 2. Redis Streams监控

```bash
# 连接Redis
redis-cli

# 查看流信息
XINFO STREAM arboris:sync:events

# 查看消费者组信息
XINFO GROUPS arboris:sync:events

# 查看最新消息
XREAD COUNT 10 STREAMS arboris:sync:events $
```

### 3. 常见问题

#### Redis连接失败
- 检查Redis服务是否启动
- 检查Redis地址和端口配置
- 检查网络连接

#### JumpServer连接失败
- 检查JumpServer地址是否正确
- 检查用户名密码是否有效
- 检查网络连接和防火墙设置

#### 事件未发布
- 检查RDB模块的events.enable配置
- 检查Redis连接是否正常
- 查看RDB模块日志是否有错误

#### 事件未消费
- 检查JumpServer同步服务是否启动
- 检查消费者组配置是否正确
- 查看同步服务日志

## 扩展开发

### 添加新的事件类型

1. 在 `src/modules/jumpserver-sync/events/types.go` 中定义新的事件类型
2. 在 `src/modules/jumpserver-sync/sync/handler.go` 中添加处理逻辑
3. 在RDB模块相应位置添加事件发布

### 自定义映射规则

编辑 `src/modules/jumpserver-sync/etc/jumpserver-sync.yml` 中的映射配置：

```yaml
sync:
  mapping:
    node_rules:
      - pattern: "^/公司/"
        target_prefix: "/Company/"
    machine_rules:
      - condition: "cate=server"
        platform: "Linux"
        protocols:
          - name: "ssh"
            port: 22
```

## 性能优化

### 1. 批量处理

调整消费者批量大小：

```yaml
sync:
  consumer:
    batch_size: 50  # 增加批量处理大小
```

### 2. 并发处理

可以启动多个同步服务实例，使用不同的consumer名称：

```yaml
sync:
  consumer:
    consumer: "jumpserver-sync-002"  # 不同实例使用不同名称
```

### 3. 流修剪

定期清理Redis Streams中的旧消息：

```bash
# 保留最近10000条消息
redis-cli XTRIM arboris:sync:events MAXLEN 10000
```

## 安全考虑

1. **Redis安全**: 配置Redis密码和访问控制
2. **JumpServer认证**: 使用API Token而非用户名密码
3. **网络安全**: 使用VPN或内网连接
4. **日志安全**: 避免在日志中记录敏感信息

## 总结

通过以上集成，Arboris RDB模块的节点和资源绑定变更将自动同步到JumpServer，实现了资产管理的统一和自动化。
