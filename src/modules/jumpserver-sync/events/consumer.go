package events

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/toolkits/pkg/logger"
)

// EventHandler 事件处理器接口
type EventHandler interface {
	HandleEvent(event *Event) error
}

// Consumer Redis Streams事件消费者
type Consumer struct {
	client       *redis.Client
	streamName   string
	groupName    string
	consumerName string
	batchSize    int64
	pollInterval time.Duration
	handler      EventHandler
	stopChan     chan struct{}
}

// NewConsumer 创建新的事件消费者
func NewConsumer(redisAddr, password string, db int, streamName, groupName, consumerName string, batchSize int64, pollInterval time.Duration, handler EventHandler) (*Consumer, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     redisAddr,
		Password: password,
		DB:       db,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to redis: %v", err)
	}

	consumer := &Consumer{
		client:       client,
		streamName:   streamName,
		groupName:    groupName,
		consumerName: consumerName,
		batchSize:    batchSize,
		pollInterval: pollInterval,
		handler:      handler,
		stopChan:     make(chan struct{}),
	}

	// 创建消费者组
	if err := consumer.createConsumerGroup(); err != nil {
		return nil, err
	}

	return consumer, nil
}

// createConsumerGroup 创建消费者组
func (c *Consumer) createConsumerGroup() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 尝试创建消费者组，使用MKSTREAM选项自动创建流
	result := c.client.XGroupCreateMkStream(ctx, c.streamName, c.groupName, "0")
	if err := result.Err(); err != nil {
		// 如果组已存在，忽略错误
		if !strings.Contains(err.Error(), "BUSYGROUP") {
			return fmt.Errorf("failed to create consumer group: %v", err)
		}
	}

	logger.Infof("Consumer group created or already exists: stream=%s, group=%s, consumer=%s",
		c.streamName, c.groupName, c.consumerName)

	return nil
}

// Start 启动消费者
func (c *Consumer) Start() {
	logger.Infof("Starting event consumer: stream=%s, group=%s, consumer=%s, batch_size=%d, poll_interval=%v",
		c.streamName, c.groupName, c.consumerName, c.batchSize, c.pollInterval)

	go c.consumeLoop()
}

// Stop 停止消费者
func (c *Consumer) Stop() {
	logger.Info("Stopping event consumer")
	close(c.stopChan)
}

// consumeLoop 消费循环
func (c *Consumer) consumeLoop() {
	ticker := time.NewTicker(c.pollInterval)
	defer ticker.Stop()

	for {
		select {
		case <-c.stopChan:
			logger.Info("Consumer stopped")
			return
		case <-ticker.C:
			c.consumeBatch()
		}
	}
}

// consumeBatch 批量消费消息
func (c *Consumer) consumeBatch() {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 从消费者组读取消息
	result := c.client.XReadGroup(ctx, &redis.XReadGroupArgs{
		Group:    c.groupName,
		Consumer: c.consumerName,
		Streams:  []string{c.streamName, ">"},
		Count:    c.batchSize,
		Block:    time.Second,
	})

	if err := result.Err(); err != nil {
		if err != redis.Nil {
			logger.Infof("Failed to read from stream: %v", err)
		}
		return
	}

	streams := result.Val()
	if len(streams) == 0 {
		return
	}

	// 处理消息
	for _, stream := range streams {
		for _, message := range stream.Messages {
			if err := c.processMessage(message); err != nil {
				logger.Infof("Failed to process message %s: %v", message.ID, err)
				continue
			}

			// 确认消息处理完成
			if err := c.ackMessage(message.ID); err != nil {
				logger.Infof("Failed to acknowledge message %s: %v", message.ID, err)
			}
		}
	}
}

// processMessage 处理单个消息
func (c *Consumer) processMessage(message redis.XMessage) error {
	// 解析消息为事件
	event, err := c.parseMessage(message)
	if err != nil {
		return fmt.Errorf("failed to parse message: %v", err)
	}

	logger.Infof("Processing event: stream_id=%s, event_id=%s, event_type=%s",
		message.ID, event.ID, event.Type)

	// 调用事件处理器
	if err := c.handler.HandleEvent(event); err != nil {
		return fmt.Errorf("handler failed to process event: %v", err)
	}

	logger.Infof("Event processed successfully: stream_id=%s, event_id=%s, event_type=%s",
		message.ID, event.ID, event.Type)

	return nil
}

// parseMessage 解析Redis消息为事件
func (c *Consumer) parseMessage(message redis.XMessage) (*Event, error) {
	event := &Event{
		Data:     make(map[string]interface{}),
		Metadata: make(map[string]string),
	}

	for key, value := range message.Values {
		strValue := fmt.Sprintf("%v", value)

		switch key {
		case "id":
			event.ID = strValue
		case "type":
			event.Type = EventType(strValue)
		case "source":
			event.Source = strValue
		case "timestamp":
			if ts, err := strconv.ParseInt(strValue, 10, 64); err == nil {
				event.Timestamp = ts
			}
		default:
			if strings.HasPrefix(key, "data.") {
				dataKey := strings.TrimPrefix(key, "data.")
				// 尝试反序列化JSON字符串
				if jsonValue := tryParseJSON(strValue); jsonValue != nil {
					event.Data[dataKey] = jsonValue
				} else {
					event.Data[dataKey] = value
				}
			} else if strings.HasPrefix(key, "metadata.") {
				metaKey := strings.TrimPrefix(key, "metadata.")
				event.Metadata[metaKey] = strValue
			}
		}
	}

	return event, nil
}

// ackMessage 确认消息
func (c *Consumer) ackMessage(messageID string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	result := c.client.XAck(ctx, c.streamName, c.groupName, messageID)
	return result.Err()
}

// tryParseJSON 尝试解析JSON字符串
func tryParseJSON(str string) interface{} {
	// 只有当字符串看起来像JSON时才尝试解析
	if len(str) == 0 {
		return nil
	}

	// 简单检查是否可能是JSON
	firstChar := str[0]
	if firstChar != '[' && firstChar != '{' && firstChar != '"' {
		return nil
	}

	var result interface{}
	if err := json.Unmarshal([]byte(str), &result); err == nil {
		return result
	}

	return nil
}

// Close 关闭消费者
func (c *Consumer) Close() error {
	c.Stop()
	return c.client.Close()
}
