package http

import (
	"arboris/src/common/address"
	"context"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"arboris/src/modules/jumpserver-sync/config"

	"github.com/gin-gonic/gin"
)

var srv = &http.Server{
	ReadTimeout:    30 * time.Second,
	WriteTimeout:   30 * time.Second,
	MaxHeaderBytes: 1 << 20,
}

var skipPaths = []string{"/health"}

func Start() {
	c := config.Config

	if strings.ToLower(c.HTTP.Mode) == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	r := gin.New()
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	Config(r)
	srv.Addr = address.GetHTTPListen("jumpserver-sync")
	srv.Handler = r

	go func() {
		fmt.Println("http.listening:", srv.Addr)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			fmt.Printf("listening %s occur error: %s\n", srv.Addr, err)
			os.Exit(3)
		}
	}()
}

// Shutdown 关闭HTTP服务器
func Shutdown() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		fmt.Println("cannot shutdown http server:", err)
		os.Exit(2)
	}

	// catching ctx.Done(). timeout of 5 seconds.
	select {
	case <-ctx.Done():
		fmt.Println("shutdown http server timeout of 5 seconds.")
	default:
		fmt.Println("http server stopped")
	}
}

// Config 配置路由
func Config(r *gin.Engine) {
	// 健康检查
	r.GET("/health", healthCheck)

	// 服务信息
	r.GET("/info", serviceInfo)
}

// healthCheck 健康检查
func healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"timestamp": time.Now().Unix(),
	})
}

// serviceInfo 服务信息
func serviceInfo(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"version": "1.0.0",
		"uptime":  time.Now().Unix(),
		"config": gin.H{
			"redis": gin.H{
				"addr": config.Config.Redis.Addr,
				"db":   config.Config.Redis.DB,
			},
			"jumpserver": gin.H{
				"base_url":     config.Config.JumpServer.BaseURL,
				"organization": config.Config.JumpServer.Organization,
			},
			"sync": gin.H{
				"stream":        config.Config.Sync.Consumer.Stream,
				"group":         config.Config.Sync.Consumer.Group,
				"consumer":      config.Config.Sync.Consumer.Consumer,
				"batch_size":    config.Config.Sync.Consumer.BatchSize,
				"poll_interval": config.Config.Sync.Consumer.PollInterval,
			},
		},
	})
}

// ApiResponse 通用API响应结构
type ApiResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// renderJSON 渲染JSON响应
func renderJSON(c *gin.Context, code int, message string, data interface{}) {
	c.JSON(code, ApiResponse{
		Code:    code,
		Message: message,
		Data:    data,
	})
}
