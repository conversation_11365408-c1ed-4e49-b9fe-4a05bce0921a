package auth

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"time"

	"arboris/src/models"
	"arboris/src/modules/rdb/cache"
	"arboris/src/modules/rdb/config"
	"arboris/src/toolkits/i18n"
	pkgcache "github.com/toolkits/pkg/cache"
	"github.com/toolkits/pkg/logger"
)

const (
	ChangePasswordURL = "/change-password"
	loginModeFifo     = true
	pwdHistorySize    = 4
)

type Authenticator struct {
	extraMode     bool
	whiteList     bool
	debug         bool
	debugUser     string
	frozenTime    int64
	writenOffTime int64
	userExpire    bool

	ctx    context.Context
	cancel context.CancelFunc
}

// description:"enable user expire control, active -> frozen -> writen-off"
func New(cf config.AuthExtraSection) *Authenticator {
	if !cf.Enable {
		return &Authenticator{}
	}

	return &Authenticator{
		extraMode:     true,
		whiteList:     cf.<PERSON><PERSON>ist,
		debug:         cf.Debug,
		debugUser:     cf.DebugUser,
		frozenTime:    86400 * int64(cf.FrozenDays),
		writenOffTime: 86400 * int64(cf.WritenOffDays),
	}
}

func (p *Authenticator) WhiteListAccess(user *models.User, remoteAddr string) error {
	if !p.extraMode || !p.whiteList || (p.debug && user.Username != p.debugUser) {
		return nil
	}

	if err := models.WhiteListAccess(remoteAddr); err != nil {
		return err
	}
	return nil
}

func (p *Authenticator) PostLogin(user *models.User, loginErr error) (err error) {
	now := time.Now().Unix()
	defer func() {
		if user == nil {
			return
		}
		if err == nil {
			user.LoggedAt = now
		}
		user.Update("status", "login_err_num", "locked_at", "updated_at", "logged_at")
	}()

	if !p.extraMode || user == nil || (p.debug && user.Username != p.debugUser) {
		err = loginErr
		return
	}

	cf := cache.AuthConfig()

	if user.Type == models.USER_T_TEMP && (now < user.ActiveBegin || user.ActiveEnd < now) {
		err = _e("Temporary user has expired")
		return
	}

	status := user.Status
retry:
	switch user.Status {
	case models.USER_S_ACTIVE:
		err = activeUserAccess(cf, user, loginErr)
	case models.USER_S_INACTIVE:
		err = inactiveUserAccess(cf, user, loginErr)
	case models.USER_S_LOCKED:
		err = lockedUserAccess(cf, user, loginErr)
	case models.USER_S_FROZEN:
		err = frozenUserAccess(cf, user, loginErr)
	case models.USER_S_WRITEN_OFF:
		err = writenOffUserAccess(cf, user, loginErr)
	default:
		err = _e("Invalid user status %d", user.Status)
	}

	// if user's status has been changed goto retry
	if user.Status != status {
		status = user.Status
		goto retry
	}
	return
}

func (p *Authenticator) ChangePassword(user *models.User, password string) (err error) {
	defer func() {
		if err == nil {
			err = user.Update("password", "passwords",
				"pwd_updated_at", "updated_at")
		}
	}()

	changePassword := func() error {
		pwd, err := models.CryptoPass(password)
		if err != nil {
			return err
		}

		now := time.Now().Unix()
		user.Password = pwd
		user.PwdUpdatedAt = now
		user.UpdatedAt = now
		return nil
	}

	// precheck
	cf := cache.AuthConfig()

	if p.extraMode {
		if err = checkPassword(cf, password); err != nil {
			return
		}
	}

	if err = changePassword(); err != nil {
		return
	}

	var passwords []string
	err = json.Unmarshal([]byte(user.Passwords), &passwords)
	if err != nil {
		// reset passwords
		passwords = []string{user.Password}
		b, _ := json.Marshal(passwords)
		user.Passwords = string(b)
		err = nil
		return
	}

	if p.extraMode {
		for i, pwd := range passwords {
			if i >= cf.PwdHistorySize {
				break
			}
			if user.Password == pwd {
				err = _e("The password is the same as the old password")
				return
			}
		}
	}

	passwords = append(passwords, user.Password)

	historySize := pwdHistorySize
	if cf.PwdHistorySize > historySize {
		historySize = cf.PwdHistorySize
	}
	if n := len(passwords) - historySize; n > 0 {
		passwords = passwords[n:]
	}

	b, _ := json.Marshal(passwords)
	user.Passwords = string(b)
	return
}

func (p *Authenticator) CheckPassword(password string) error {
	if !p.extraMode {
		return nil
	}
	return checkPassword(cache.AuthConfig(), password)
}

func (p *Authenticator) DeleteSession(sid string) error {
	s, err := models.SessionGet(sid)
	if err != nil {
		return err
	}

	if !p.extraMode {
		pkgcache.Delete("sid." + s.Sid)
		return models.SessionDelete(s.Sid)
	}
	return deleteSession(s)
}

func (p *Authenticator) Stop() error {
	p.cancel()
	return nil
}

func (p *Authenticator) Start() error {
	p.ctx, p.cancel = context.WithCancel(context.Background())

	if !p.extraMode {
		return nil
	}

	go func() {
		t := time.NewTicker(5 * time.Second)
		defer t.Stop()
		for {
			select {
			case <-p.ctx.Done():
				return
			case <-t.C:
				p.cleanupSession()
			}
		}
	}()

	go func() {
		t := time.NewTicker(time.Hour)
		defer t.Stop()

		for {
			select {
			case <-p.ctx.Done():
				return
			case <-t.C:
				p.updateUserStatus()
			}
		}
	}()
	return nil
}

func (p *Authenticator) PrepareUser(user *models.User) {
	if !p.extraMode {
		return
	}

	cf := cache.AuthConfig()
	if cf.PwdExpiresIn > 0 {
		user.PwdExpiresAt = user.PwdUpdatedAt + cf.PwdExpiresIn*86400*30
	}
}

// cleanup rdb.session
func (p *Authenticator) cleanupSession() {
	now := time.Now().Unix()
	cf := cache.AuthConfig()

	// idle session cleanup
	if cf.MaxConnIdleTime > 0 {
		expiresAt := now - cf.MaxConnIdleTime*60
		sessions := []models.Session{}
		if err := models.DB["rdb"].SQL("select * from session where updated_at < ? and username <> '' ", expiresAt).Find(&sessions); err != nil {
			logger.Errorf("token idle time cleanup err %s", err)
		}

		logger.Debugf("find %d idle sessions that should be clean up", len(sessions))

		for _, s := range sessions {
			if p.debug && s.Username != p.debugUser {
				continue
			}

			logger.Debugf("[idle] deleteSession %s %s", s.Username, s.Sid)
			deleteSession(&s)
		}
	}
}

func (p *Authenticator) updateUserStatus() {
	now := time.Now().Unix()
	if p.frozenTime > 0 {
		// 3个月以上未登录，用户自动变为休眠状态
		if _, err := models.DB["rdb"].Exec("update user set status=?, updated_at=?, locked_at=? where ((logged_at > 0 and logged_at<?) or (logged_at = 0 and create_at < ?)) and status in (?,?,?)",
			models.USER_S_FROZEN, now, now, now-p.frozenTime,
			models.USER_S_ACTIVE, models.USER_S_INACTIVE, models.USER_S_LOCKED); err != nil {
			logger.Errorf("update user status error %s", err)
		}
	}

	if p.writenOffTime > 0 {
		// 变为休眠状态后1年未激活，用户自动变为已注销状态
		if _, err := models.DB["rdb"].Exec("update user set status=?, updated_at=? where locked_at<? and status=?",
			models.USER_S_WRITEN_OFF, now, now-p.writenOffTime, models.USER_S_FROZEN); err != nil {
			logger.Errorf("update user status error %s", err)
		}
	}

	// reset login err num before 24 hours ago
	if _, err := models.DB["rdb"].Exec("update user set login_err_num=0, updated_at=? where updated_at<? and login_err_num>0", now, now-86400); err != nil {
		logger.Errorf("update user login err num error %s", err)
	}

}

func activeUserAccess(cf *models.AuthConfig, user *models.User, loginErr error) error {
	now := time.Now().Unix()

	if loginErr != nil {
		if cf.MaxNumErr > 0 {
			user.UpdatedAt = now
			user.LoginErrNum++
			if user.LoginErrNum >= cf.MaxNumErr {
				user.Status = models.USER_S_LOCKED
				user.LockedAt = now
				return nil
			}
			return _e("Incorrect login/password %d times, you still have %d chances",
				user.LoginErrNum, cf.MaxNumErr-user.LoginErrNum)
		} else {
			return loginErr
		}
	}

	user.LoginErrNum = 0
	user.UpdatedAt = now

	if cf.MaxSessionNumber > 0 && !loginModeFifo {
		if n, err := models.SessionUserAll(user.Username); err != nil {
			return err
		} else if n >= cf.MaxSessionNumber {
			return _e("The limited sessions %d", cf.MaxSessionNumber)
		}
	}

	return nil
}
func inactiveUserAccess(cf *models.AuthConfig, user *models.User, loginErr error) error {
	return _e("User is inactive")
}
func lockedUserAccess(cf *models.AuthConfig, user *models.User, loginErr error) error {
	now := time.Now().Unix()
	if now-user.LockedAt > cf.LockTime*60 {
		user.Status = models.USER_S_ACTIVE
		user.LoginErrNum = 0
		user.UpdatedAt = now
		return nil
	}
	return _e("User is locked, unlock at %dm later", int(math.Ceil(float64(user.LockedAt+cf.LockTime*60-now))/60.0))
}

func frozenUserAccess(cf *models.AuthConfig, user *models.User, loginErr error) error {
	return _e("User is frozen")
}

func writenOffUserAccess(cf *models.AuthConfig, user *models.User, loginErr error) error {
	return _e("User is writen off")
}

func checkPassword(cf *models.AuthConfig, passwd string) error {
	indNum := [4]int{0, 0, 0, 0}
	spCode := []byte{'!', '@', '#', '$', '%', '^', '&', '*', '_', '-', '~', '.', ',', '<', '>', '/', ';', ':', '|', '?', '+', '='}

	if cf.PwdMinLenght > 0 && len(passwd) < cf.PwdMinLenght {
		return _e("Password too short (min:%d)", cf.PwdMinLenght)
	}

	passwdByte := []byte(passwd)

	for _, i := range passwdByte {

		if i >= 'A' && i <= 'Z' {
			indNum[0] = 1
			continue
		}

		if i >= 'a' && i <= 'z' {
			indNum[1] = 1
			continue
		}

		if i >= '0' && i <= '9' {
			indNum[2] = 1
			continue
		}

		has := false
		for _, s := range spCode {
			if i == s {
				indNum[3] = 1
				has = true
				break
			}
		}

		if !has {
			return _e("character: %s not supported", string(i))
		}
	}

	if cf.PwdMustIncludeFlag&models.PWD_INCLUDE_UPPER > 0 && indNum[0] == 0 {
		return _e("Invalid Password, must include %s", _s("Upper char"))
	}

	if cf.PwdMustIncludeFlag&models.PWD_INCLUDE_LOWER > 0 && indNum[1] == 0 {
		return _e("Invalid Password, must include %s", _s("Lower char"))
	}

	if cf.PwdMustIncludeFlag&models.PWD_INCLUDE_NUMBER > 0 && indNum[2] == 0 {
		return _e("Invalid Password, must include %s", _s("Number"))
	}

	if cf.PwdMustIncludeFlag&models.PWD_INCLUDE_SPEC_CHAR > 0 && indNum[3] == 0 {
		return _e("Invalid Password, must include %s", _s("Special char"))
	}

	return nil
}

func deleteSession(s *models.Session) error {
	pkgcache.Delete("sid." + s.Sid)
	return models.SessionDelete(s.Sid)
}

func _e(format string, a ...interface{}) error {
	return fmt.Errorf(i18n.Sprintf(format, a...))
}

func _s(format string, a ...interface{}) string {
	return i18n.Sprintf(format, a...)
}
