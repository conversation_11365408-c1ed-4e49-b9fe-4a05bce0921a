package auth

import (
	"arboris/src/models"
	"arboris/src/modules/rdb/config"
)

var defaultAuth Authenticator

func Init(cf config.AuthExtraSection) {
	defaultAuth = *New(cf)
}

func WhiteListAccess(user *models.User, remoteAddr string) error {
	return defaultAuth.WhiteListAccess(user, remoteAddr)
}

// PostLogin check user status after login
func PostLogin(user *models.User, loginErr error) error {
	return defaultAuth.PostLogin(user, loginErr)
}

func ChangePassword(user *models.User, password string) error {
	return defaultAuth.ChangePassword(user, password)
}

func CheckPassword(password string) error {
	return defaultAuth.CheckPassword(password)
}

func DeleteSession(sid string) error {
	return defaultAuth.DeleteSession(sid)
}

func Start() error {
	return defaultAuth.Start()
}

func PrepareUser(user *models.User) {
	defaultAuth.PrepareUser(user)
}
