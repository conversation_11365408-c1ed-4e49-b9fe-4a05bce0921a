// @title Arboris RDB API
// @version 1.0
// @description Arboris RDB 模块 API 文档
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8000
// @BasePath /

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name X-User-Token
// @description User token for API authentication

// @securityDefinitions.apikey CookieAuth
// @in cookie
// @name ecmc-sid
// @description Session cookie for web authentication

package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	_ "github.com/go-sql-driver/mysql"

	"github.com/toolkits/pkg/logger"
	"github.com/toolkits/pkg/runner"

	_ "arboris/docs-rdb" // swagger docs
	"arboris/src/common/loggeri"
	"arboris/src/models"
	"arboris/src/modules/rdb/auth"
	"arboris/src/modules/rdb/cache"
	"arboris/src/modules/rdb/config"
	"arboris/src/modules/rdb/events"
	"arboris/src/modules/rdb/http"
	"arboris/src/modules/rdb/session"
	"arboris/src/toolkits/i18n"
)

var (
	vers *bool
	help *bool
	conf *string

	version = "No Version Provided"
)

func init() {
	vers = flag.Bool("v", false, "display the version.")
	help = flag.Bool("h", false, "print this help.")
	conf = flag.String("f", "", "specify configuration file.")
	flag.Parse()

	if *vers {
		fmt.Println("Version:", version)
		os.Exit(0)
	}

	if *help {
		flag.Usage()
		os.Exit(0)
	}

	runner.Init()
	fmt.Println("runner.cwd:", runner.Cwd)
	fmt.Println("runner.hostname:", runner.Hostname)
}

func main() {
	parseConf()

	loggeri.Init(config.Config.Logger)

	// 初始化数据库和相关数据
	models.InitMySQL("rdb", "ams")

	models.InitSalt()
	models.InitRooter()

	i18n.Init(config.Config.I18n)

	cache.Start()
	session.Init()

	auth.Init(config.Config.Auth.ExtraMode)
	auth.Start()

	// 初始化事件生产者
	if config.Config.Events.Enable {
		if err := events.InitProducer(
			config.Config.Redis.Addr,
			config.Config.Redis.Password,
			config.Config.Redis.DB,
			config.Config.Events.StreamName,
		); err != nil {
			logger.Errorf("Failed to initialize event producer: %v", err)
		}
	}

	// 初始化JumpServer同步事件发布器
	if err := events.InitJSEventProducer(); err != nil {
		logger.Errorf("Failed to initialize JumpServer sync event producer: %v", err)
	}

	http.Start()

	endingProc()
}

func parseConf() {
	if err := config.Parse(); err != nil {
		fmt.Println("cannot parse configuration file:", err)
		os.Exit(1)
	}
}

func endingProc() {
	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)
	select {
	case <-c:
		fmt.Printf("stop signal caught, stopping... pid=%d\n", os.Getpid())
	}

	logger.Close()
	http.Shutdown()
	session.Stop()
	cache.Stop()
	events.Close()

	fmt.Println("process stopped successfully")
}
