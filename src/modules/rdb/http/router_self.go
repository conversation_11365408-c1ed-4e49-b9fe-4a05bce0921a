package http

import (
	"arboris/src/models"
	"arboris/src/modules/rdb/auth"
	"arboris/src/modules/rdb/config"

	"github.com/gin-gonic/gin"
)

// @Summary 获取个人信息
// @Description 获取当前登录用户的个人信息
// @Tags 个人中心
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Success 200 {object} ApiResponse{dat=models.User} "个人信息"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/self/profile [get]
func selfProfileGet(c *gin.Context) {
	renderData(c, loginUser(c), nil)
}

type selfProfileForm struct {
	Dispname string `json:"dispname"`
	Phone    string `json:"phone"`
	Email    string `json:"email"`
	Im       string `json:"im"`
	Portrait string `json:"portrait"`
	Intro    string `json:"intro"`
}

// @Summary 更新个人信息
// @Description 更新当前登录用户的个人信息
// @Tags 个人中心
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param profile body selfProfileForm true "个人信息"
// @Success 200 {object} ApiResponse "更新成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/self/profile [put]
func selfProfilePut(c *gin.Context) {
	var f selfProfileForm
	bind(c, &f)

	user := loginUser(c)
	user.Dispname = f.Dispname
	user.Phone = f.Phone
	user.Email = f.Email
	user.Im = f.Im
	user.Portrait = f.Portrait
	user.Intro = f.Intro

	renderMessage(c, user.Update("dispname", "phone", "email", "im", "portrait", "intro"))
}

type selfPasswordForm struct {
	Username string `json:"username" binding:"required"`
	OldPass  string `json:"oldpass" binding:"required"`
	NewPass  string `json:"newpass" binding:"required"`
}

// @Summary 修改个人密码
// @Description 用户修改自己的登录密码
// @Tags 个人中心
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param password body selfPasswordForm true "密码修改信息"
// @Success 200 {object} ApiResponse "密码修改成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/self/password [put]
func selfPasswordPut(c *gin.Context) {
	var f selfPasswordForm
	bind(c, &f)

	err := func() error {
		user, err := models.UserMustGet("username=?", f.Username)
		if err != nil {
			return err
		}
		oldpass, err := models.CryptoPass(f.OldPass)
		if err != nil {
			return err
		}
		if user.Password != oldpass {
			return _e("Incorrect old password")
		}

		if err := auth.ChangePassword(user, f.NewPass); err != nil {
			return err
		}

		return nil
	}()

	renderMessage(c, err)
}

// @Summary 获取个人Token列表
// @Description 获取当前用户的所有API Token
// @Tags 个人中心
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Success 200 {object} ApiResponse{dat=[]models.UserToken} "Token列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/self/token [get]
func selfTokenGets(c *gin.Context) {
	objs, err := models.UserTokenGets("user_id=?", loginUser(c).Id)
	renderData(c, objs, err)
}

// @Summary 创建个人Token
// @Description 为当前用户创建新的API Token
// @Tags 个人中心
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Success 200 {object} ApiResponse{dat=models.UserToken} "新创建的Token"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/self/token [post]
func selfTokenPost(c *gin.Context) {
	user := loginUser(c)
	obj, err := models.UserTokenNew(user.Id, user.Username)
	renderData(c, obj, err)
}

type selfTokenForm struct {
	Token string `json:"token"`
}

// @Summary 重置个人Token
// @Description 重置指定的API Token
// @Tags 个人中心
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param token body selfTokenForm true "Token信息"
// @Success 200 {object} ApiResponse{dat=models.UserToken} "重置后的Token"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/self/token [put]
func selfTokenPut(c *gin.Context) {
	user := loginUser(c)

	var f selfTokenForm
	bind(c, &f)

	obj, err := models.UserTokenReset(user.Id, f.Token)
	renderData(c, obj, err)
}

// @Summary 获取用户全局权限
// @Description 获取当前登录用户的全局权限操作列表
// @Tags 个人中心
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Success 200 {object} ApiResponse{dat=map[string]interface{}} "用户权限操作列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/self/perms/global [get]
func permGlobalOps(c *gin.Context) {
	user := loginUser(c)
	operations := make(map[string]struct{})

	if user.IsRoot == 1 {
		for _, system := range config.GlobalOps {
			for _, group := range system.Groups {
				for _, op := range group.Ops {
					operations[op.En] = struct{}{}
				}
			}
		}

		renderData(c, operations, nil)
		return
	}

	roleIds, err := models.RoleIdsGetByUserId(user.Id)
	dangerous(err)

	ops, err := models.OperationsOfRoles(roleIds)
	dangerous(err)

	for _, op := range ops {
		operations[op] = struct{}{}
	}

	renderData(c, operations, err)
}

func v1PermGlobalOps(c *gin.Context) {
	user, err := models.UserGet("username=?", queryStr(c, "username"))
	dangerous(err)

	operations := make(map[string]struct{})

	if user.IsRoot == 1 {
		for _, system := range config.GlobalOps {
			for _, group := range system.Groups {
				for _, op := range group.Ops {
					operations[op.En] = struct{}{}
				}
			}
		}

		renderData(c, operations, nil)
		return
	}

	roleIds, err := models.RoleIdsGetByUserId(user.Id)
	dangerous(err)

	ops, err := models.OperationsOfRoles(roleIds)
	dangerous(err)

	for _, op := range ops {
		operations[op] = struct{}{}
	}

	renderData(c, operations, err)
}
