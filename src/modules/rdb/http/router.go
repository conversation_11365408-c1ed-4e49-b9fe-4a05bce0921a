package http

import (
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

func Config(r *gin.Engine) {
	// 添加 CORS 中间件
	r.Use(corsMiddleware())

	// Swagger 文档路由 - 启用认证信息持久化，默认展开所有分类
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler,
		ginSwagger.PersistAuthorization(true),
		ginSwagger.DocExpansion("list"),
	))

	notLogin := r.Group("/api/rdb")
	{
		notLogin.GET("/ping", ping)
		notLogin.GET("/ops/global", globalOpsGet)
		notLogin.GET("/ops/local", localOpsGet)
		notLogin.GET("/roles/global", globalRoleGet)
		notLogin.GET("/roles/local", localRoleGet)
		notLogin.GET("/pwd-rules", pwdRulesGet)

		notLogin.POST("/users/invite", userInvitePost)

	}

	sessionStarted := r.Group("/api/rdb").Use(shouldStartSession())
	{
		sessionStarted.POST("/auth/login", login)
		sessionStarted.GET("/auth/logout", logout)
	}

	rootLogin := r.Group("/api/rdb").Use(shouldBeRoot())
	{
		rootLogin.POST("/users", userAddPost)
		rootLogin.GET("/root/teams/all", teamAllGetRoot)

		rootLogin.GET("/user/:id/profile", userProfileGet)
		rootLogin.PUT("/user/:id/profile", userProfilePut)
		rootLogin.PUT("/user/:id/password", userPasswordPut)
		rootLogin.PUT("/user/:id/enable", userEnable)
		rootLogin.PUT("/user/:id/disable", userDisable)
		rootLogin.DELETE("/user/:id", userDel)

		rootLogin.GET("/configs/auth", authConfigsGet)
		rootLogin.PUT("/configs/auth", authConfigsPut)
		rootLogin.POST("/auth/white-list", whiteListPost)
		rootLogin.GET("/auth/white-list", whiteListsGet)
		rootLogin.GET("/auth/white-list/:id", whiteListGet)
		rootLogin.PUT("/auth/white-list/:id", whiteListPut)
		rootLogin.DELETE("/auth/white-list/:id", whiteListDel)

		rootLogin.GET("/log/login", loginLogGets)
		rootLogin.GET("/log/operation", operationLogGets)

		rootLogin.POST("/roles", roleAddPost)
		rootLogin.PUT("/role/:id", rolePut)
		rootLogin.DELETE("/role/:id", roleDel)
		rootLogin.GET("/role/:id", roleDetail)
		rootLogin.GET("/role/:id/users", roleGlobalUsersGet)
		rootLogin.PUT("/role/:id/users/bind", roleGlobalUsersBind)
		rootLogin.PUT("/role/:id/users/unbind", roleGlobalUsersUnbind)

	}

	userLogin := r.Group("/api/rdb").Use(shouldBeLogin())
	{
		userLogin.GET("/resoplogs", operationLogResGets)

		userLogin.GET("/users", userListGet)
		userLogin.GET("/users/invite", userInviteGet)

		userLogin.GET("/self/profile", selfProfileGet)
		userLogin.PUT("/self/profile", selfProfilePut)
		userLogin.PUT("/self/password", selfPasswordPut)
		userLogin.GET("/self/token", selfTokenGets)
		userLogin.POST("/self/token", selfTokenPost)
		userLogin.PUT("/self/token", selfTokenPut)
		userLogin.GET("/self/perms/global", permGlobalOps)

		userLogin.GET("/teams/all", teamAllGet)
		userLogin.GET("/teams/mine", teamMineGet)
		userLogin.POST("/teams", teamAddPost)
		userLogin.PUT("/team/:id", teamPut)
		userLogin.GET("/team/:id", teamDetail)
		userLogin.PUT("/team/:id/users/bind", teamUserBind)
		userLogin.PUT("/team/:id/users/unbind", teamUserUnbind)
		userLogin.DELETE("/team/:id", teamDel)

		userLogin.GET("/node-cates", nodeCateGets)
		userLogin.GET("/node-cates/fields", nodeCateFieldGets)
		userLogin.GET("/node-cates/field/:id", nodeCateFieldGet)

		userLogin.POST("/nodes", nodePost)
		userLogin.GET("/nodes", nodeGets)
		userLogin.GET("/node/:id", nodeGet)
		userLogin.PUT("/node/:id", nodePut)
		userLogin.DELETE("/node/:id", nodeDel)
		userLogin.GET("/node/:id/fields", nodeFieldGets)
		userLogin.PUT("/node/:id/fields", nodeFieldPuts)
		userLogin.GET("/node/:id/roles", rolesUnderNodeGets)
		userLogin.POST("/node/:id/roles", rolesUnderNodePost)
		userLogin.DELETE("/node/:id/roles", rolesUnderNodeDel)
		userLogin.DELETE("/node/:id/roles/try", rolesUnderNodeDelTry)
		userLogin.GET("/node/:id/resources", resourceUnderNodeGet)
		userLogin.GET("/node/:id/resources/cate-count", renderNodeResourcesCountByCate)
		userLogin.POST("/node/:id/resources/bind", resourceBindNode)
		userLogin.POST("/node/:id/resources/unbind", resourceUnbindNode)
		userLogin.PUT("/node/:id/resources/note", resourceUnderNodeNotePut)
		userLogin.PUT("/node/:id/resources/labels", resourceUnderNodeLabelsPut)

		userLogin.GET("/tree", treeUntilLeafGets)
		userLogin.GET("/tree/projs", treeUntilProjectGets)
		userLogin.GET("/tree/orgs", treeUntilOrganizationGets)

		userLogin.GET("/resources/search", resourceSearchGet)
		userLogin.PUT("/resources/note", resourceNotePut)
		userLogin.PUT("/resources/note/try", resourceNotePutTry)
		userLogin.GET("/resources/bindings", resourceBindingsGet)
		userLogin.GET("/resources/orphan", resourceOrphanGet)

		userLogin.GET("/resources/cate-count", renderAllResourcesCountByCate)

		// 是否在某个节点上有权限做某个操作(即资源权限点)
		userLogin.GET("/can-do-node-op", v1CandoNodeOp)
		// 同时校验多个操作权限点
		userLogin.GET("/can-do-node-ops", v1CandoNodeOps)
	}

	v1 := r.Group("/v1/rdb").Use(shouldBeService())
	{
		// 获取这个节点下的所有资源，跟给前端的API(/api/rdb/node/:id/resources会根据当前登陆用户获取有权限看到的资源列表)不同
		v1.GET("/node/:id/resources", v1ResourcesUnderNodeGet)
		// RDB作为一个类似CMDB的东西，接收各个子系统注册过来的资源，其他资源都是依托于项目创建的，RDB会根据nid自动挂载资源到相应节点
		v1.POST("/resources/register", v1ResourcesRegisterPost)
		// 资源销毁的时候，需要从库里清掉，同时需要把节点挂载关系也删除，一个资源可能挂载在多个节点，都要统统干掉
		v1.POST("/resources/unregister", v1ResourcesUnregisterPost)

		v1.POST("/containers/bind", v1ContainersBindPost)
		v1.POST("/container/sync", v1ContainerSyncPost)

		v1.GET("/nodes", nodeGets)
		v1.GET("/node/:id", nodeGet)
		v1.GET("/node-include-trash/:id", nodeIncludeTrashGet)
		v1.GET("/node/:id/projs", v1treeUntilProjectGetsByNid)
		v1.GET("/tree/projs", v1TreeUntilProjectGets)
		v1.GET("/tree", v1TreeUntilTypGets)

		// 外部系统推送一些操作日志过来，RDB统一存储，实际用MQ会更好一些
		v1.POST("/resoplogs", v1OperationLogResPost)

		// 是否有权限做一些全局操作(即页面权限点)
		v1.GET("/can-do-global-op", v1CandoGlobalOp)
		// 是否在某个节点上有权限做某个操作(即资源权限点)
		v1.GET("/can-do-node-op", v1CandoNodeOp)
		// 同时校验多个操作权限点
		v1.GET("/can-do-node-ops", v1CandoNodeOps)

		// 获取用户、团队相关信息
		v1.GET("/get-user-by-uuid", v1UserGetByUUID)
		v1.GET("/get-users-by-uuids", v1UserGetByUUIDs)
		v1.GET("/get-users-by-ids", v1UserGetByIds)
		v1.GET("/get-users-by-names", v1UserGetByNames)
		v1.GET("/get-user-by-token", v1UserGetByToken)
		v1.GET("/get-users-by-query", userListGet)
		v1.GET("/get-teams-by-ids", v1TeamGetByIds)
		v1.GET("/get-user-ids-by-team-ids", v1UserIdsGetByTeamIds)

		v1.GET("/users", v1UserListGet)

		// 第三方系统获取某个用户的所有权限点
		v1.GET("/perms/global", v1PermGlobalOps)

		// session
		v1.GET("/sessions/:sid", v1SessionGet)
		v1.GET("/sessions/:sid/user", v1SessionGetUser)
		v1.GET("/sessions", v1SessionListGet)
		v1.DELETE("/sessions/:sid", v1SessionDelete)

		// 第三方系统同步权限表的数据
		v1.GET("/table/sync/role-operation", v1RoleOperationGets)
		v1.GET("/table/sync/role-global-user", v1RoleGlobalUserGets)
	}
}
