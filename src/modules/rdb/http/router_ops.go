package http

import (
	"arboris/src/modules/rdb/config"

	"github.com/gin-gonic/gin"
)

// @Summary 获取页面角色权限配置
// @Description 获取系统定义的页面权限配置
// @Tags 系统管理
// @Accept json
// @Produce json
// @Success 200 {object} ApiResponse{dat=config.OpsStruct} "全局权限配置"
// @Router /api/rdb/ops/global [get]
func globalOpsGet(c *gin.Context) {
	renderData(c, config.GlobalOps, nil)
}

// @Summary 获取资源角色权限配置
// @Description 获取系统定义的资源权限配置
// @Tags 系统管理
// @Accept json
// @Produce json
// @Success 200 {object} ApiResponse{dat=config.OpsStruct} "本地权限配置"
// @Router /api/rdb/ops/local [get]
func localOpsGet(c *gin.Context) {
	renderData(c, config.LocalOps, nil)
}
