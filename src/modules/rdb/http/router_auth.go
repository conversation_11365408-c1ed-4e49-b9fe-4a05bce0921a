package http

import (
	"arboris/src/modules/rdb/cache"
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/logger"
	"github.com/toolkits/pkg/str"

	"arboris/src/models"
	"arboris/src/modules/rdb/auth"
	"arboris/src/modules/rdb/session"
)

// 白名单列表响应结构
type WhiteListResponse struct {
	List  []models.WhiteList `json:"list"`
	Total int                `json:"total"`
}

// 登录响应结构
type LoginResponse struct {
	User *models.User `json:"user"`
	Msg  string       `json:"msg"`
}

// @Summary 用户登录
// @Description 用户通过用户名和密码登录系统
// @Tags 认证管理
// @Accept json
// @Produce json
// @Param login body loginInput true "登录信息"
// @Success 200 {object} ApiResponse "登录成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "认证失败"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/auth/login [post]
func login(c *gin.Context) {
	var in loginInput
	bind(c, &in)
	in.RemoteAddr = c.ClientIP()

	err := func() error {
		if err := in.Validate(); err != nil {
			return err
		}

		user, err := authLogin(in.v1LoginInput())
		if err != nil {
			logger.Debugf("login error %s", err)
			return err
		}

		sessionLogin(c, user.Username, in.RemoteAddr, "")
		return nil
	}()
	renderMessage(c, err)
}

type authRedirect struct {
	User *models.User `json:"user"`
	Msg  string       `json:"msg"`
}

// @Summary 用户登出
// @Description 用户登出系统，清除会话信息
// @Tags 认证管理
// @Accept json
// @Produce json
// @Security CookieAuth
// @Success 200 {object} ApiResponse "登出成功"
// @Router /api/rdb/auth/logout [get]
func logout(c *gin.Context) {
	ret := &authRedirect{}

	username := sessionUsername(c)
	if username == "" {
		renderData(c, ret, nil)
		return
	}

	sessionDestory(c)
	ret.Msg = "logout successfully"

	renderData(c, ret, nil)

	models.LoginLogNew(username, c.ClientIP(), "out", nil)
}

type loginInput struct {
	Username   string   `json:"username"`
	Password   string   `json:"password"`
	Type       string   `json:"type" description:"password|"`
	Args       []string `json:"args" description:""`
	RemoteAddr string   `json:"remote_addr" description:"use for server account(v1)"`
}

func (p *loginInput) Validate() error {
	if len(p.Args) == 0 {
		if str.Dangerous(p.Username) {
			return _e("Username %s is invalid", p.Username)
		}
		if len(p.Username) > 64 {
			return _e("Username %s too long > 64", p.Username)
		}
		p.Args = []string{p.Username, p.Password}
	}

	if len(p.Args) == 0 {
		return _e("Unable to get login arguments")
	}
	return nil
}

func (p *loginInput) v1LoginInput() *v1LoginInput {
	return &v1LoginInput{
		Type:       p.Type,
		Args:       p.Args,
		RemoteAddr: p.RemoteAddr,
	}
}

type v1LoginInput struct {
	Type       string   `param:"data" json:"type"`
	Args       []string `param:"data" json:"args"`
	RemoteAddr string   `param:"data" json:"remote_addr"`
}

func (p *v1LoginInput) Validate() error {
	if p.Type == "" {
		p.Type = models.LOGIN_T_PWD
	}

	if len(p.Args) == 0 {
		return _e("Unable to get login arguments")
	}

	return nil
}

// authLogin called by /v1/rdb/login, /api/rdb/auth/login
func authLogin(in *v1LoginInput) (user *models.User, err error) {
	if err = in.Validate(); err != nil {
		return
	}
	defer func() {
		models.LoginLogNew(in.Args[0], in.RemoteAddr, "in", err)
	}()

	switch strings.ToLower(in.Type) {
	case models.LOGIN_T_PWD:
		user, err = models.PassLogin(in.Args[0], in.Args[1])
	default:
		err = _e("Invalid login type %s", in.Type)
	}

	if user != nil && in.RemoteAddr != "" {
		if err := auth.WhiteListAccess(user, in.RemoteAddr); err != nil {
			return nil, _e("Deny Access from %s with whitelist control", in.RemoteAddr)
		}
	}

	if err = auth.PostLogin(user, err); err != nil {
		return nil, err
	}

	return user, nil
}

type rstPasswordInput struct {
	Type     string `json:"type"`
	Arg      string `json:"arg"`
	Code     string `json:"code"`
	Password string `json:"password"`
	DryRun   bool   `json:"dryRun"`
}

func (p *rstPasswordInput) Validate() error {
	if p.Type == "" {
		return _e("Unable to get type, sms-code | email-code")
	}
	if p.Arg == "" {
		return _e("Unable to get code arg")
	}
	if !p.DryRun && p.Password == "" {
		return _e("Unable to get password")
	}
	return nil
}

func rstPassword(c *gin.Context) {
	var in rstPasswordInput
	bind(c, &in)

	err := func() error {
		if err := in.Validate(); err != nil {
			return err
		}

		var user *models.User

		switch in.Type {
		case models.LOGIN_T_SMS:
			user, _ = models.UserGet("phone=?", in.Arg)
		case models.LOGIN_T_EMAIL:
			user, _ = models.UserGet("email=?", in.Arg)
		default:
			return fmt.Errorf("invalid type %s", in.Type)
		}

		if user == nil {
			return _e("Cannot find the user by %s", in.Arg)
		}

		lc, err := models.LoginCodeGet("username=? and code=? and login_type=?", user.Username, in.Code, models.LOGIN_T_RST)
		if err != nil {
			return _e("Invalid code")
		}

		if time.Now().Unix()-lc.CreatedAt > models.LOGIN_EXPIRES_IN {
			return _e("The code has expired")
		}

		if in.DryRun {
			return nil
		}

		// update password
		if err := auth.ChangePassword(user, in.Password); err != nil {
			return err
		}

		lc.Del()
		return nil
	}()

	if err != nil {
		renderData(c, nil, err)
	} else {
		renderData(c, "reset successfully", nil)
	}
}

// @Summary 获取认证配置
// @Description 获取系统认证配置信息，包括密码策略、会话管理等设置
// @Tags 认证配置
// @Accept json
// @Produce json
// @Success 200 {object} ApiResponse{dat=models.AuthConfig} "认证配置信息"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/configs/auth [get]
// @Security ApiKeyAuth
// @Security CookieAuth
func authConfigsGet(c *gin.Context) {
	config, err := models.AuthConfigGet()
	renderData(c, config, err)
}

// @Summary 更新认证配置
// @Description 更新系统认证配置信息，包括密码策略、会话管理等设置
// @Tags 认证配置
// @Accept json
// @Produce json
// @Param config body models.AuthConfig true "认证配置信息"
// @Success 200 {object} ApiResponse "更新成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/configs/auth [put]
// @Security ApiKeyAuth
// @Security CookieAuth
func authConfigsPut(c *gin.Context) {
	var in models.AuthConfig
	bind(c, &in)

	err := models.AuthConfigSet(&in)
	renderData(c, "", err)
}

type createWhiteListInput struct {
	StartIp   string `json:"startIp"`
	EndIp     string `json:"endIp"`
	StartTime int64  `json:"startTime"`
	EndTime   int64  `json:"endTime"`
}

// @Summary 创建白名单条目
// @Description 创建新的IP白名单条目，用于访问控制
// @Tags 白名单管理
// @Accept json
// @Produce json
// @Param whitelist body createWhiteListInput true "白名单信息"
// @Success 200 {object} ApiResponse{dat=map[string]interface{}} "创建成功，返回ID"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/auth/white-list [post]
// @Security ApiKeyAuth
// @Security CookieAuth
func whiteListPost(c *gin.Context) {
	var in createWhiteListInput
	bind(c, &in)

	username := loginUser(c).Username
	ts := time.Now().Unix()

	wl := models.WhiteList{
		StartIp:   in.StartIp,
		EndIp:     in.EndIp,
		StartTime: in.StartTime,
		EndTime:   in.EndTime,
		CreatedAt: ts,
		UpdatedAt: ts,
		Creator:   username,
		Updater:   username,
	}
	if err := wl.Validate(); err != nil {
		bomb("Invalid arguments %s", err)
	}
	dangerous(wl.Save())

	renderData(c, gin.H{"id": wl.Id}, nil)
}

// @Summary 获取白名单列表
// @Description 获取IP白名单列表，支持分页和搜索
// @Tags 白名单管理
// @Accept json
// @Produce json
// @Param limit query int false "每页数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Param query query string false "搜索关键字"
// @Success 200 {object} ApiResponse{dat=WhiteListResponse} "白名单列表"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/auth/white-list [get]
// @Security ApiKeyAuth
// @Security CookieAuth
func whiteListsGet(c *gin.Context) {
	limit := queryInt(c, "limit", 20)
	query := queryStr(c, "query", "")

	total, err := models.WhiteListTotal(query)
	dangerous(err)

	list, err := models.WhiteListGets(query, limit, offset(c, limit))
	dangerous(err)

	renderData(c, gin.H{
		"list":  list,
		"total": total,
	}, nil)
}

// @Summary 获取单个白名单条目
// @Description 根据ID获取指定的白名单条目详情
// @Tags 白名单管理
// @Accept json
// @Produce json
// @Param id path int true "白名单ID"
// @Success 200 {object} ApiResponse{dat=models.WhiteList} "白名单详情"
// @Failure 404 {object} ApiResponse "白名单不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/auth/white-list/{id} [get]
// @Security ApiKeyAuth
// @Security CookieAuth
func whiteListGet(c *gin.Context) {
	id := urlParamInt64(c, "id")
	ret, err := models.WhiteListGet("id=?", id)
	renderData(c, ret, err)
}

type updateWhiteListInput struct {
	StartIp   string `json:"startIp"`
	EndIp     string `json:"endIp"`
	StartTime int64  `json:"startTime"`
	EndTime   int64  `json:"endTime"`
}

// @Summary 更新白名单条目
// @Description 更新指定ID的白名单条目信息
// @Tags 白名单管理
// @Accept json
// @Produce json
// @Param id path int true "白名单ID"
// @Param whitelist body updateWhiteListInput true "更新的白名单信息"
// @Success 200 {object} ApiResponse "更新成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 404 {object} ApiResponse "白名单不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/auth/white-list/{id} [put]
// @Security ApiKeyAuth
// @Security CookieAuth
func whiteListPut(c *gin.Context) {
	var in updateWhiteListInput
	bind(c, &in)

	wl, err := models.WhiteListGet("id=?", urlParamInt64(c, "id"))
	if err != nil {
		bomb("Cannot found white list")
	}

	wl.StartIp = in.StartIp
	wl.EndIp = in.EndIp
	wl.StartTime = in.StartTime
	wl.EndTime = in.EndTime
	wl.UpdatedAt = time.Now().Unix()
	wl.Updater = loginUser(c).Username

	if err := wl.Validate(); err != nil {
		bomb("Invalid arguments %s", err)
	}

	renderMessage(c, wl.Update("start_ip", "end_ip", "start_time", "end_time", "updated_at", "updater"))
}

// @Summary 删除白名单条目
// @Description 删除指定ID的白名单条目
// @Tags 白名单管理
// @Accept json
// @Produce json
// @Param id path int true "白名单ID"
// @Success 200 {object} ApiResponse "删除成功"
// @Failure 404 {object} ApiResponse "白名单不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/auth/white-list/{id} [delete]
// @Security ApiKeyAuth
// @Security CookieAuth
func whiteListDel(c *gin.Context) {
	wl, err := models.WhiteListGet("id=?", urlParamInt64(c, "id"))
	dangerous(err)

	renderMessage(c, wl.Del())
}

// @Summary 获取密码规则
// @Description 获取系统密码策略规则，用于前端显示密码要求
// @Tags 系统管理
// @Accept json
// @Produce json
// @Success 200 {object} ApiResponse{dat=[]string} "密码规则列表"
// @Router /api/rdb/pwd-rules [get]
func pwdRulesGet(c *gin.Context) {
	cf := cache.AuthConfig()
	renderData(c, cf.PwdRules(), nil)
}

func sessionDestory(c *gin.Context) (sid string, err error) {
	if sid, err = session.GetSid(c.Request); sid == "" {
		return
	}

	if e := auth.DeleteSession(sid); e != nil {
		logger.Debugf("auth.deleteSession sid %s err %v", sid, e)
	}

	session.Destroy(c.Writer, c.Request)

	return
}

func v1SessionGet(c *gin.Context) {
	s, err := models.SessionGetWithCache(urlParamStr(c, "sid"))
	renderData(c, s, err)
}

func v1SessionGetUser(c *gin.Context) {
	user, err := models.SessionGetUserWithCache(urlParamStr(c, "sid"))
	renderData(c, user, err)
}

func v1SessionListGet(c *gin.Context) {
	limit := queryInt(c, "limit", 20)
	query := queryStr(c, "query", "")

	list, total, err := models.SessionAndTotalGets(query, limit, offset(c, limit))

	renderData(c, gin.H{
		"list":  list,
		"total": total,
	}, err)
}

func v1SessionDelete(c *gin.Context) {
	sid := urlParamStr(c, "sid")
	logger.Debugf("session del sid %s", sid)
	renderMessage(c, auth.DeleteSession(sid))
}
