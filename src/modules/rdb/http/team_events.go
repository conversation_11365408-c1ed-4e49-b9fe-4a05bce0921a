package http

import (
	"arboris/src/models"
	jsEvents "arboris/src/modules/jumpserver-sync/events"
	"arboris/src/modules/rdb/events"
)

// publishTeamCreateEvent 发布团队创建事件
func publishTeamCreateEvent(teamID int64, ident, name, note string, mgmt int, creatorID int64) {
	teamData := &jsEvents.TeamEventData{
		ID:      teamID,
		Ident:   ident,
		Name:    name,
		Note:    note,
		Mgmt:    mgmt,
		Members: []int64{},          // 初始为空，创建者会通过BindUser添加
		Admins:  []int64{creatorID}, // 创建者默认为管理员
		Creator: creatorID,
	}
	events.PublishTeamCreateEvent(teamData)
}

// publishTeamUpdateEvent 发布团队更新事件
func publishTeamUpdateEvent(team *models.Team) {
	// 获取团队成员信息
	members, _ := getTeamMembers(team.Id, false) // 普通成员
	admins, _ := getTeamMembers(team.Id, true)   // 管理员

	teamData := &jsEvents.TeamEventData{
		ID:      team.Id,
		Ident:   team.Ident,
		Name:    team.Name,
		Note:    team.Note,
		Mgmt:    team.Mgmt,
		Members: members,
		Admins:  admins,
		Creator: 0, // 更新时不需要creator信息
	}
	events.PublishTeamUpdateEvent(teamData)
}

// publishTeamDeleteEvent 发布团队删除事件
func publishTeamDeleteEvent(team *models.Team) {
	teamData := &jsEvents.TeamEventData{
		ID:      team.Id,
		Ident:   team.Ident,
		Name:    team.Name,
		Note:    team.Note,
		Mgmt:    team.Mgmt,
		Members: []int64{}, // 删除时不需要成员信息
		Admins:  []int64{}, // 删除时不需要管理员信息
		Creator: 0,         // 删除时不需要creator信息
	}
	events.PublishTeamDeleteEvent(teamData)
}

// publishTeamAddUserEvent 发布团队添加用户事件
func publishTeamAddUserEvent(team *models.Team, addedUserIds []int64) {
	// 获取当前所有团队成员信息
	members, _ := getTeamMembers(team.Id, false) // 普通成员
	admins, _ := getTeamMembers(team.Id, true)   // 管理员

	teamData := &jsEvents.TeamEventData{
		ID:               team.Id,
		Ident:            team.Ident,
		Name:             team.Name,
		Note:             team.Note,
		Mgmt:             team.Mgmt,
		Members:          members,
		Admins:           admins,
		Creator:          0, // 成员变更时不需要creator信息
		OperationUserIDs: addedUserIds, // 传递被添加的用户ID列表
	}

	// 发布专门的团队添加成员事件
	events.PublishTeamAddMemberEvent(teamData)
}

// publishTeamRemoveUserEvent 发布团队移除用户事件
func publishTeamRemoveUserEvent(team *models.Team, removedUserIds []int64) {
	// 获取当前所有团队成员信息（已经移除了指定用户）
	members, _ := getTeamMembers(team.Id, false) // 普通成员
	admins, _ := getTeamMembers(team.Id, true)   // 管理员

	teamData := &jsEvents.TeamEventData{
		ID:               team.Id,
		Ident:            team.Ident,
		Name:             team.Name,
		Note:             team.Note,
		Mgmt:             team.Mgmt,
		Members:          members,
		Admins:           admins,
		Creator:          0, // 成员变更时不需要creator信息
		OperationUserIDs: removedUserIds, // 传递被移除的用户ID列表
	}

	// 发布专门的团队移除成员事件
	events.PublishTeamRemoveMemberEvent(teamData)
}

// getTeamMembers 获取团队成员ID列表
func getTeamMembers(teamID int64, isAdmin bool) ([]int64, error) {
	var adminFlag int
	if isAdmin {
		adminFlag = 1
	} else {
		adminFlag = 0
	}

	// 使用现有的UserIdsByTeamIds方法
	userIds, err := models.UserIdsByTeamIds([]int64{teamID}, adminFlag)
	if err != nil {
		return []int64{}, err
	}

	return userIds, nil
}
