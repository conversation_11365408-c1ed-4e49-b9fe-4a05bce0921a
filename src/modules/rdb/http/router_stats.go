package http

import (
	"arboris/src/models"

	"github.com/gin-gonic/gin"
)

type rdbStats struct {
	Login *models.Stats
}

var (
	stats *rdbStats
)

func initStats() {
	stats = &rdbStats{
		Login: models.MustNewStats("login"),
	}
}

// @Summary 获取系统计数器
// @Description 获取系统各项统计计数器，如登录次数等
// @Tags 系统统计
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Success 200 {object} ApiResponse{dat=map[string]int64} "统计计数器"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/counter [get]
func counterGet(c *gin.Context) {
	renderData(c, map[string]int64{
		"login": stats.Login.Get(),
	}, nil)
}
