package http

import (
	"github.com/gin-gonic/gin"
)

type v1ContainersRegisterItem struct {
	UUID   string `json:"uuid"`
	Ident  string `json:"ident"`
	Name   string `json:"name"`
	Labels string `json:"labels"`
	Extend string `json:"extend"`
	Cate   string `json:"cate"`
	NID    int64  `json:"nid"`
}

func (f v1ContainersRegisterItem) Validate() {
	if f.Cate == "" {
		bomb("cate is blank")
	}

	if f.UUID == "" {
		bomb("uuid is blank")
	}

	if f.Ident == "" {
		bomb("ident is blank")
	}
}

// @Summary 绑定容器资源
// @Description 批量绑定容器资源到节点
// @Tags V1接口
// @Accept json
// @Produce json
// @Security ServiceAuth
// @Param body body []v1ContainersRegisterItem true "容器资源列表"
// @Success 200 {object} ApiResponse "绑定成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /v1/rdb/containers/bind [post]
func v1ContainersBindPost(c *gin.Context) {
	var items []v1ContainersRegisterItem
	bind(c, &items)

	count := len(items)
	if count == 0 {
		bomb("items empty")
	}

	resourceHttpRegister(count, items)

	renderMessage(c, nil)
}
