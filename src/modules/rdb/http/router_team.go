package http

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/str"

	"arboris/src/models"
)

// 团队列表响应结构
type TeamListResponse struct {
	List  []models.Team `json:"list"`
	Total int64         `json:"total"`
}

// TeamDetailResponse 团队详情响应
type TeamDetailResponse struct {
	List  []models.User `json:"list" description:"团队成员列表"`
	Team  models.Team   `json:"team" description:"团队信息"`
	Total int64         `json:"total" description:"成员总数"`
}

// @Summary 获取所有团队列表
// @Description 获取系统中所有团队的列表，支持分页和搜索
// @Tags 团队管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param limit query int false "每页数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Param query query string false "搜索关键字"
// @Success 200 {object} ApiResponse{dat=TeamListResponse} "团队列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/teams/all [get]
func teamAllGet(c *gin.Context) {
	teamAllGetImpl(c)
}

// @Summary 获取所有团队列表(超管)
// @Description 超级管理员获取系统中所有团队的列表，支持分页和搜索
// @Tags 团队管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param limit query int false "每页数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Param query query string false "搜索关键字"
// @Success 200 {object} ApiResponse{dat=TeamListResponse} "团队列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/root/teams/all [get]
func teamAllGetRoot(c *gin.Context) {
	teamAllGetImpl(c)
}

func teamAllGetImpl(c *gin.Context) {
	limit := queryInt(c, "limit", 20)
	query := queryStr(c, "query", "")

	total, err := models.TeamTotal(query)
	dangerous(err)

	list, err := models.TeamGets(query, limit, offset(c, limit))
	dangerous(err)

	renderData(c, gin.H{
		"list":  list,
		"total": total,
	}, nil)
}

// @Summary 获取我的团队列表
// @Description 获取当前用户所属的团队列表，支持分页和搜索
// @Tags 团队管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param limit query int false "每页数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Param query query string false "搜索关键字"
// @Success 200 {object} ApiResponse{dat=TeamListResponse} "我的团队列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/teams/mine [get]
func teamMineGet(c *gin.Context) {
	limit := queryInt(c, "limit", 20)
	query := queryStr(c, "query", "")

	user := loginUser(c)

	teamIds, err := models.TeamIdsByUserId(user.Id)
	dangerous(err)

	if len(teamIds) == 0 {
		renderZeroPage(c)
		return
	}

	total, err := models.TeamTotalInIds(teamIds, query)
	dangerous(err)

	list, err := models.TeamGetsInIds(teamIds, query, limit, offset(c, limit))
	dangerous(err)

	renderData(c, gin.H{
		"list":  list,
		"total": total,
	}, nil)
}

// @Summary 获取团队详情
// @Description 获取指定团队的详细信息，包括团队成员列表
// @Tags 团队管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "团队ID"
// @Param query query string false "搜索关键字"
// @Param limit query int false "每页数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Success 200 {object} ApiResponse{dat=TeamDetailResponse} "团队详情和成员列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "团队不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/team/{id} [get]
func teamDetail(c *gin.Context) {
	query := queryStr(c, "query", "")
	limit := queryInt(c, "limit", 20)

	team := Team(urlParamInt64(c, "id"))

	total, err := team.UsersTotal(query)
	dangerous(err)

	list, err := team.UsersGet(query, limit, offset(c, limit))
	dangerous(err)

	renderData(c, gin.H{
		"list":  list,
		"total": total,
		"team":  team,
	}, nil)
}

type teamForm struct {
	Ident string `json:"ident"`
	Name  string `json:"name"`
	Note  string `json:"note"`
	Mgmt  int    `json:"mgmt"`
}

// @Summary 创建团队
// @Description 创建新的团队
// @Tags 团队管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param team body teamForm true "团队信息"
// @Success 200 {object} ApiResponse{dat=models.Team} "创建的团队信息"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/teams [post]
func teamAddPost(c *gin.Context) {
	var f teamForm
	bind(c, &f)

	me := loginUser(c)

	lastid, err := models.TeamAdd(f.Ident, f.Name, f.Note, f.Mgmt, me.Id)
	if err == nil {
		go models.OperationLogNew(me.Username, "team", lastid, fmt.Sprintf("TeamCreate ident: %s name: %s", f.Ident, f.Name))

		// 发布团队创建事件到JumpServer同步
		go publishTeamCreateEvent(lastid, f.Ident, f.Name, f.Note, f.Mgmt, me.Id)
	}

	renderMessage(c, err)
}

// @Summary 更新团队信息
// @Description 更新指定团队的信息
// @Tags 团队管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "团队ID"
// @Param team body teamForm true "团队信息"
// @Success 200 {object} ApiResponse "更新成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "团队不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/team/{id} [put]
func teamPut(c *gin.Context) {
	me := loginUser(c)

	var f teamForm
	bind(c, &f)

	t := Team(urlParamInt64(c, "id"))

	can, err := me.CanModifyTeam(t)
	dangerous(err)

	if !can {
		bomb("no privilege")
	}

	arr := make([]string, 0, 2)
	if f.Name != t.Name {
		arr = append(arr, fmt.Sprintf("name: %s -> %s", t.Name, f.Name))
	}

	if f.Note != t.Note {
		arr = append(arr, fmt.Sprintf("note: %s -> %s", t.Note, f.Note))
	}

	err = t.Modify(f.Name, f.Note, f.Mgmt)
	if err == nil && len(arr) > 0 {
		content := strings.Join(arr, ", ")
		go models.OperationLogNew(me.Username, "team", t.Id, "TeamModify "+content)

		//发布团队更新事件到JumpServer同步
		//go publishTeamUpdateEvent(t)
	}

	renderMessage(c, err)
}

type teamUserBindForm struct {
	AdminIds  []int64 `json:"admin_ids"`
	MemberIds []int64 `json:"member_ids"`
}

// @Summary 绑定团队成员
// @Description 为团队添加管理员和普通成员
// @Tags 团队管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "团队ID"
// @Param users body teamUserBindForm true "用户绑定信息"
// @Success 200 {object} ApiResponse "绑定成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "团队不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/team/{id}/users/bind [put]
func teamUserBind(c *gin.Context) {
	me := loginUser(c)

	var f teamUserBindForm
	bind(c, &f)

	team := Team(urlParamInt64(c, "id"))

	can, err := me.CanModifyTeam(team)
	dangerous(err)

	if !can {
		bomb("no privilege")
	}

	var addedUserIds []int64

	if f.AdminIds != nil && len(f.AdminIds) > 0 {
		dangerous(team.BindUser(f.AdminIds, 1))
		addedUserIds = append(addedUserIds, f.AdminIds...)
	}

	if f.MemberIds != nil && len(f.MemberIds) > 0 {
		dangerous(team.BindUser(f.MemberIds, 0))
		addedUserIds = append(addedUserIds, f.MemberIds...)
	}

	// 发布团队添加用户事件到JumpServer同步
	if len(addedUserIds) > 0 {
		go publishTeamAddUserEvent(team, addedUserIds)
	}

	renderMessage(c, nil)
}

type teamUserUnbindForm struct {
	UserIds []int64 `json:"user_ids"`
}

// @Summary 解绑团队成员
// @Description 从团队中移除指定的成员
// @Tags 团队管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "团队ID"
// @Param users body teamUserUnbindForm true "用户解绑信息"
// @Success 200 {object} ApiResponse "解绑成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "团队不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/team/{id}/users/unbind [put]
func teamUserUnbind(c *gin.Context) {
	me := loginUser(c)

	var f teamUserUnbindForm
	bind(c, &f)

	team := Team(urlParamInt64(c, "id"))

	can, err := me.CanModifyTeam(team)
	dangerous(err)

	if !can {
		bomb("no privilege")
	}

	err = team.UnbindUser(f.UserIds)
	if err == nil && len(f.UserIds) > 0 {
		// 发布团队移除用户事件到JumpServer同步
		go publishTeamRemoveUserEvent(team, f.UserIds)
	}

	renderMessage(c, err)
}

// @Summary 删除团队
// @Description 删除指定的团队
// @Tags 团队管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int true "团队ID"
// @Success 200 {object} ApiResponse "删除成功"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "团队不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/team/{id} [delete]
func teamDel(c *gin.Context) {
	me := loginUser(c)

	t, err := models.TeamGet("id=?", urlParamInt64(c, "id"))
	dangerous(err)

	if t == nil {
		renderMessage(c, nil)
		return
	}

	can, err := me.CanModifyTeam(t)
	dangerous(err)

	if !can {
		bomb("no privilege")
	}

	err = t.Del()
	if err == nil {
		go models.OperationLogNew(me.Username, "team", t.Id, fmt.Sprintf("TeamDelete ident: %s name: %s", t.Ident, t.Name))

		// 发布团队删除事件到JumpServer同步
		go publishTeamDeleteEvent(t)
	}

	renderMessage(c, err)
}

func belongTeamsGet(c *gin.Context) {
	username := queryStr(c, "username")
	isadminStr := queryStr(c, "is_admin", "")

	user, err := models.UserGet("username=?", username)
	dangerous(err)

	if user == nil {
		bomb("no such user: %s", username)
	}

	var ids []int64
	if isadminStr == "" {
		ids, err = models.TeamIdsByUserId(user.Id)
		dangerous(err)
	} else {
		isadminInt, err := strconv.Atoi(isadminStr)
		dangerous(err)

		ids, err = models.TeamIdsByUserId(user.Id, isadminInt)
		dangerous(err)
	}

	ret, err := models.TeamGetByIds(ids)
	renderData(c, ret, err)
}

func teamGetByIdent(c *gin.Context) {
	ident := urlParamStr(c, "ident")
	team, err := models.TeamGet("ident=?", ident)
	renderData(c, team, err)
}

func isTeamMember(c *gin.Context) {
	username := queryStr(c, "username")
	teamIdent := queryStr(c, "team")

	user, err := models.UserGet("username=?", username)
	dangerous(err)

	if user == nil {
		bomb("no such user: %s", username)
	}

	team, err := models.TeamGet("ident=?", teamIdent)
	dangerous(err)

	if team == nil {
		bomb("no such team[%s]", teamIdent)
	}

	has, err := models.TeamHasMember(team.Id, user.Id)
	renderData(c, has, err)
}

func v1TeamGetByIds(c *gin.Context) {
	ids := queryStr(c, "ids")
	teams, err := models.TeamGetByIds(str.IdsInt64(ids))
	renderData(c, teams, err)
}
