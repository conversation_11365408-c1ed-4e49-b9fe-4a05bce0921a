package http

import (
	"arboris/src/models"

	"github.com/gin-gonic/gin"
)

// @Summary 获取页面角色列表
// @Description 获取系统中所有全局角色的列表，无权限限制
// @Tags 角色管理
// @Accept json
// @Produce json
// @Success 200 {object} ApiResponse{dat=[]models.Role} "全局角色列表"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/roles/global [get]
func globalRoleGet(c *gin.Context) {
	list, err := models.RoleFind("global")
	renderData(c, list, err)
}

// @Summary 获取资源角色列表
// @Description 获取系统中所有局部角色的列表，无权限限制
// @Tags 角色管理
// @Accept json
// @Produce json
// @Success 200 {object} ApiResponse{dat=[]models.Role} "局部角色列表"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/roles/local [get]
func localRoleGet(c *gin.Context) {
	list, err := models.RoleFind("local")
	renderData(c, list, err)
}

type roleForm struct {
	Name       string   `json:"name"`
	Note       string   `json:"note"`
	Cate       string   `json:"cate"`
	Operations []string `json:"operations"`
}

// @Summary 创建角色
// @Description 创建新的角色，只有超级管理员有权限
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param role body roleForm true "角色信息"
// @Success 200 {object} ApiResponse{dat=object{id=int64}} "创建成功，返回角色ID"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/roles [post]
func roleAddPost(c *gin.Context) {
	var f roleForm
	bind(c, &f)

	r := models.Role{
		Name: f.Name,
		Note: f.Note,
		Cate: f.Cate,
	}

	dangerous(r.Save(f.Operations))

	renderData(c, gin.H{"id": r.Id}, nil)
}

// @Summary 修改角色
// @Description 修改角色的基本信息，只有超级管理员有权限
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "角色ID"
// @Param role body roleForm true "角色信息"
// @Success 200 {object} ApiResponse "修改成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "角色不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/role/{id} [put]
func rolePut(c *gin.Context) {
	var f roleForm
	bind(c, &f)

	obj := Role(urlParamInt64(c, "id"))

	renderMessage(c, obj.Modify(f.Name, f.Note, f.Cate, f.Operations))
}

// @Summary 删除角色
// @Description 删除指定角色，会删除相关联的数据，只有超级管理员有权限
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "角色ID"
// @Success 200 {object} ApiResponse "删除成功"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "角色不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/role/{id} [delete]
func roleDel(c *gin.Context) {
	obj, err := models.RoleGet("id=?", urlParamInt64(c, "id"))
	dangerous(err)

	if obj == nil {
		renderMessage(c, nil)
		return
	}

	renderMessage(c, obj.Del())
}

// @Summary 获取角色详情
// @Description 获取指定角色的详细信息，包括角色信息和权限操作列表
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "角色ID"
// @Success 200 {object} ApiResponse{dat=object{role=models.Role,operations=[]string}} "角色详情"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "角色不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/role/{id} [get]
func roleDetail(c *gin.Context) {
	obj := Role(urlParamInt64(c, "id"))

	ops, err := models.OperationsOfRoles([]int64{obj.Id})
	dangerous(err)

	renderData(c, gin.H{
		"role":       obj,
		"operations": ops,
	}, nil)
}

// @Summary 获取角色绑定的用户列表
// @Description 获取指定全局角色下绑定的用户列表，支持分页和搜索，只有管理员可以查看
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "角色ID"
// @Param limit query int false "每页数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Param query query string false "搜索关键字"
// @Success 200 {object} ApiResponse{dat=object{list=[]models.User,role=models.Role,total=int}} "角色用户信息"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "角色不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/role/{id}/users [get]
func roleGlobalUsersGet(c *gin.Context) {
	rid := urlParamInt64(c, "id")
	role := Role(rid)

	limit := queryInt(c, "limit", 20)
	query := queryStr(c, "query", "")

	ids, err := role.GlobalUserIds()
	dangerous(err)

	if len(ids) == 0 {
		renderData(c, gin.H{
			"list":  []models.User{},
			"total": 0,
			"role":  role,
		}, nil)
		return
	}

	total, err := models.UserSearchTotalInIds(ids, query)
	dangerous(err)

	list, err := models.UserSearchListInIds(ids, query, limit, offset(c, limit))
	dangerous(err)

	for i := 0; i < len(list); i++ {
		list[i].UUID = ""
	}

	renderData(c, gin.H{
		"list":  list,
		"total": total,
		"role":  role,
	}, nil)
}

// @Summary 角色绑定用户
// @Description 将指定用户绑定到全局角色，把某些用户设置为某个全局角色
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "角色ID"
// @Param body body idsForm true "用户ID列表"
// @Success 200 {object} ApiResponse "绑定成功"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "角色不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/role/{id}/users/bind [put]
func roleGlobalUsersBind(c *gin.Context) {
	var f idsForm
	bind(c, &f)

	obj, err := models.RoleGet("id=?", urlParamInt64(c, "id"))
	dangerous(err)

	if obj == nil {
		bomb("no such role")
	}

	renderMessage(c, obj.BindUsers(f.Ids))
}

// @Summary 角色解绑用户
// @Description 将指定用户从全局角色中解绑，把某些用户从某个全局角色中移除
// @Tags 角色管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "角色ID"
// @Param body body idsForm true "用户ID列表"
// @Success 200 {object} ApiResponse "解绑成功"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "角色不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/role/{id}/users/unbind [put]
func roleGlobalUsersUnbind(c *gin.Context) {
	var f idsForm
	bind(c, &f)

	obj, err := models.RoleGet("id=?", urlParamInt64(c, "id"))
	dangerous(err)

	if obj == nil {
		bomb("no such role")
	}

	renderMessage(c, obj.UnbindUsers(f.Ids))
}

func v1RoleOperationGets(c *gin.Context) {
	objs, err := models.RoleOperationAll()
	renderData(c, objs, err)
}
