package http

import (
	"arboris/src/models"
	"strings"

	"github.com/gin-gonic/gin"
)

// @Summary 检查全局操作权限
// @Description 检查指定用户是否有权限执行全局操作
// @Tags 权限管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param username query string true "用户名"
// @Param op query string true "操作权限点"
// @Success 200 {object} ApiResponse{dat=bool} "是否有权限"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/can-do-global-op [get]
func v1CandoGlobalOp(c *gin.Context) {
	username := queryStr(c, "username")
	operation := queryStr(c, "op")
	has, err := models.UsernameCandoGlobalOp(username, operation)
	renderData(c, has, err)
}

// @Summary 检查节点操作权限
// @Description 检查指定用户是否有权限在指定节点上执行操作
// @Tags 权限管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param username query string true "用户名"
// @Param op query string true "操作权限点"
// @Param nid query int64 true "节点ID"
// @Success 200 {object} ApiResponse{dat=bool} "是否有权限"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/can-do-node-op [get]
func v1CandoNodeOp(c *gin.Context) {
	username := queryStr(c, "username")
	operation := queryStr(c, "op")
	nodeId := queryInt64(c, "nid")
	has, err := models.UsernameCandoNodeOp(username, operation, nodeId)
	renderData(c, has, err)
}

// @Summary 批量检查节点操作权限
// @Description 批量检查指定用户在指定节点上的多个操作权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param username query string true "用户名"
// @Param ops query string true "操作权限点列表，逗号分隔"
// @Param nid query int64 true "节点ID"
// @Success 200 {object} ApiResponse{dat=map[string]bool} "权限检查结果"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/can-do-node-ops [get]
func v1CandoNodeOps(c *gin.Context) {
	username := queryStr(c, "username")
	ops := strings.Split(queryStr(c, "ops"), ",")
	nodeId := queryInt64(c, "nid")
	node := Node(nodeId)

	user, err := models.UserGet("username=?", username)
	dangerous(err)

	ret := make(map[string]bool)

	for i := 0; i < len(ops); i++ {
		has, err := user.HasPermByNode(node, ops[i])
		dangerous(err)

		ret[ops[i]] = has
	}

	renderData(c, ret, nil)
}

// @Summary 获取全局角色用户关系
// @Description 获取所有全局角色与用户的绑定关系，用于数据同步
// @Tags V1接口
// @Accept json
// @Produce json
// @Security ServiceAuth
// @Success 200 {object} ApiResponse{dat=[]models.RoleGlobalUser} "角色用户关系列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /v1/rdb/table/sync/role-global-user [get]
func v1RoleGlobalUserGets(c *gin.Context) {
	objs, err := models.RoleGlobalUserAll()
	renderData(c, objs, err)
}
