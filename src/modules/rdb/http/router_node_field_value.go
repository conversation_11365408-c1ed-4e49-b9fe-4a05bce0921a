package http

import (
	"arboris/src/models"

	"github.com/gin-gonic/gin"
)

// @Summary 获取节点字段值
// @Description 获取指定节点的所有字段值
// @Tags 节点字段值管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "节点ID"
// @Success 200 {object} ApiResponse{dat=[]models.NodeFieldValue} "节点字段值列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "节点不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/node/{id}/fields [get]
func nodeFieldGets(c *gin.Context) {
	lst, err := models.NodeFieldValueGets(urlParamInt64(c, "id"))
	renderData(c, lst, err)
}

// @Summary 批量更新节点字段值
// @Description 批量更新指定节点的字段值
// @Tags 节点字段值管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "节点ID"
// @Param fields body []models.NodeFieldValue true "字段值列表"
// @Success 200 {object} ApiResponse "更新成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "节点不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/node/{id}/fields [put]
func nodeFieldPuts(c *gin.Context) {
	var objs []models.NodeFieldValue
	bind(c, &objs)

	id := urlParamInt64(c, "id")
	node := Node(id)

	loginUser(c).CheckPermByNode(node, "rdb_node_modify")

	renderMessage(c, models.NodeFieldValuePuts(id, objs))
}
