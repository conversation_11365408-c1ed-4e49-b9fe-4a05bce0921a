package http

import (
	jsEvents "arboris/src/modules/jumpserver-sync/events"
	"arboris/src/modules/rdb/events"
	"fmt"

	"arboris/src/models"

	"github.com/gin-gonic/gin"
)

// @Summary 获取节点下的角色列表
// @Description 获取指定节点下的角色绑定列表，支持分页和用户筛选
// @Tags 节点角色管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "节点ID"
// @Param limit query int false "每页数量" default(20)
// @Param username query string false "用户名筛选"
// @Success 200 {object} ApiResponse{dat=object{list=[]interface{},total=int64}} "角色列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "节点不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/node/{id}/roles [get]
func rolesUnderNodeGets(c *gin.Context) {
	limit := queryInt(c, "limit", 20)
	username := queryStr(c, "username", "")
	node := Node(urlParamInt64(c, "id"))

	total, err := node.RoleTotal(username)
	dangerous(err)

	list, err := node.RoleList(username, limit, offset(c, limit))
	dangerous(err)

	m, err := models.RoleMap("local")
	dangerous(err)

	size := len(list)
	var usernames []string
	for i := 0; i < size; i++ {
		usernames = append(usernames, list[i].Username)
	}

	users, err := models.UserGetByNames(usernames)
	dangerous(err)

	usersMap := make(map[string]models.User)
	for i := 0; i < len(users); i++ {
		usersMap[users[i].Username] = users[i]
	}

	for i := 0; i < size; i++ {
		list[i].RoleTxt = m[list[i].RoleId]
		if user, exists := usersMap[list[i].Username]; exists {
			list[i].Dispname = user.Dispname
		}
	}

	renderData(c, gin.H{
		"list":  list,
		"total": total,
	}, nil)
}

type roleUnderNodeForm struct {
	Usernames []string `json:"usernames"`
	RoleId    int64    `json:"role_id"`
}

// @Summary 为节点分配角色
// @Description 为指定节点的用户分配角色权限
// @Tags 节点角色管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "节点ID"
// @Param body body roleUnderNodeForm true "角色分配参数"
// @Success 200 {object} ApiResponse "分配成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "节点不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/node/{id}/roles [post]
func rolesUnderNodePost(c *gin.Context) {
	var f roleUnderNodeForm
	bind(c, &f)

	node := Node(urlParamInt64(c, "id"))
	role := Role(f.RoleId)

	me := loginUser(c)
	me.CheckPermByNode(node, "rdb_perm_grant")

	count := len(f.Usernames)
	var userIDs []int64

	for i := 0; i < count; i++ {
		user, err := models.UserGet("username=?", f.Usernames[i])
		dangerous(err)

		if user == nil {
			bomb("no such user: %s", f.Usernames[i])
		}

		nodeRole := &models.NodeRole{
			NodeId:   node.Id,
			Username: f.Usernames[i],
			RoleId:   f.RoleId,
		}
		err = nodeRole.Save()
		if err == nil {
			detail := fmt.Sprintf("NodeRoleBind node: %s, username: %s, role: %s", node.Path, f.Usernames[i], role.Name)
			go models.OperationLogNew(me.Username, "node", node.Id, detail)
			userIDs = append(userIDs, user.Id)
		}
		dangerous(err)
	}

	// 发布权限授予事件到JumpServer同步
	if len(userIDs) > 0 {
		go func() {
			permissionData := &jsEvents.PermissionEventData{
				NodeID:     node.Id,
				NodePath:   node.Path,
				UserIDs:    userIDs,
				TeamIDs:    []int64{}, // 这里是用户权限，不是团队权限
				Operation:  "grant",
				Permission: "rw", // 默认读写权限，可以根据角色配置
				RoleID:     f.RoleId,
				RoleName:   role.Name,
			}
			events.PublishPermissionGrantEvent(permissionData)
		}()
	}

	renderMessage(c, nil)
}

type roleUnderNodeDelForm struct {
	Username string `json:"username"`
	RoleId   int64  `json:"role_id"`
}

// @Summary 删除节点角色
// @Description 删除指定节点下用户的角色权限
// @Tags 节点角色管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "节点ID"
// @Param body body roleUnderNodeDelForm true "角色删除参数"
// @Success 200 {object} ApiResponse "删除成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "节点不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/node/{id}/roles [delete]
func rolesUnderNodeDel(c *gin.Context) {
	var f roleUnderNodeDelForm
	bind(c, &f)

	node := Node(urlParamInt64(c, "id"))
	role := Role(f.RoleId)

	me := loginUser(c)
	if me.Username != f.Username {
		// 即使我没有rdb_perm_grant权限，我也可以删除我自己的权限，所以，两个username不同的时候才需要鉴权
		me.CheckPermByNode(node, "rdb_perm_grant")
	}

	err := models.NodeRoleDel(node.Id, f.RoleId, f.Username)
	if err == nil {
		detail := fmt.Sprintf("NodeRoleUnbind node: %s, username: %s, role: %s", node.Path, f.Username, role.Name)
		go models.OperationLogNew(me.Username, "node", node.Id, detail)

		// 发布权限撤销事件到JumpServer同步
		go func() {
			// 获取用户ID
			user, err := models.UserGet("username=?", f.Username)
			if err != nil || user == nil {
				return
			}
			permissionData := &jsEvents.PermissionEventData{
				NodeID:     node.Id,
				NodePath:   node.Path,
				UserIDs:    []int64{user.Id},
				TeamIDs:    []int64{}, // 这里是用户权限，不是团队权限
				Operation:  "revoke",
				Permission: "rw", // 默认读写权限，可以根据角色配置
				RoleID:     f.RoleId,
				RoleName:   role.Name,
			}
			events.PublishPermissionRevokeEvent(permissionData)
		}()
	}

	renderMessage(c, err)
}

// @Summary 测试节点角色删除权限
// @Description 测试当前用户是否有权限删除指定节点的角色
// @Tags 节点角色管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "节点ID"
// @Success 200 {object} ApiResponse "有权限"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "节点不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/node/{id}/roles/try [delete]
func rolesUnderNodeDelTry(c *gin.Context) {
	node := Node(urlParamInt64(c, "id"))

	me := loginUser(c)
	me.CheckPermByNode(node, "rdb_perm_grant")

	renderMessage(c, nil)
}
