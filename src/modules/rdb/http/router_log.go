package http

import (
	"arboris/src/models"

	"github.com/gin-gonic/gin"
)

// 登录日志列表响应结构
type LoginLogListResponse struct {
	List  []models.LoginLog `json:"list"`
	Total int64             `json:"total"`
}

// 操作日志列表响应结构
type OperationLogListResponse struct {
	List  []models.OperationLog `json:"list"`
	Total int64                 `json:"total"`
}

// @Summary 获取登录日志
// @Description 超级管理员查看所有用户的登录记录
// @Tags 日志管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param limit query int false "每页数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Param username query string false "用户名"
// @Param btime query int false "开始时间戳"
// @Param etime query int false "结束时间戳"
// @Success 200 {object} ApiResponse{dat=LoginLogListResponse} "登录日志列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/log/login [get]
func loginLogGets(c *gin.Context) {
	limit := queryInt(c, "limit", 20)
	username := queryStr(c, "username", "")
	btime := queryInt64(c, "btime")
	etime := queryInt64(c, "etime")

	total, err := models.LoginLogTotal(username, btime, etime)
	dangerous(err)

	list, err := models.LoginLogGets(username, btime, etime, limit, offset(c, limit))
	dangerous(err)

	renderData(c, gin.H{
		"list":  list,
		"total": total,
	}, nil)
}

// @Summary 获取操作日志
// @Description 超级管理员查看所有类型资源的操作记录
// @Tags 日志管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param limit query int false "每页数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Param query query string false "搜索关键字"
// @Param btime query int false "开始时间戳"
// @Param etime query int false "结束时间戳"
// @Param username query string false "用户名"
// @Param res_type query string false "资源类型"
// @Success 200 {object} ApiResponse{dat=OperationLogListResponse} "操作日志列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/log/operation [get]
func operationLogGets(c *gin.Context) {
	limit := queryInt(c, "limit", 20)
	query := queryStr(c, "query", "")
	btime := queryInt64(c, "btime")
	etime := queryInt64(c, "etime")

	total, err := models.OperationLogTotal(query, btime, etime)
	dangerous(err)

	list, err := models.OperationLogQuery(query, btime, etime, limit, offset(c, limit))
	dangerous(err)

	renderData(c, gin.H{
		"list":  list,
		"total": total,
	}, nil)
}

// @Summary 获取资源操作日志
// @Description 查询具体某个资源的操作历史记录，一般用在资源详情页面
// @Tags 操作日志
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param limit query int false "每页数量" default(20)
// @Param btime query int64 false "开始时间戳"
// @Param etime query int64 false "结束时间戳"
// @Param rescl query string false "资源类别"
// @Param resid query string false "资源ID"
// @Success 200 {object} ApiResponse{dat=object{list=[]models.OperationLog,total=int64}} "操作日志列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/resoplogs [get]
func operationLogResGets(c *gin.Context) {
	limit := queryInt(c, "limit", 20)
	btime := queryInt64(c, "btime")
	etime := queryInt64(c, "etime")
	rescl := queryStr(c, "rescl")
	resid := queryStr(c, "resid")

	total, err := models.OperationLogTotalByRes(rescl, resid, btime, etime)
	dangerous(err)

	list, err := models.OperationLogGetsByRes(rescl, resid, btime, etime, limit, offset(c, limit))
	dangerous(err)

	renderData(c, gin.H{
		"list":  list,
		"total": total,
	}, nil)
}

func v1OperationLogResPost(c *gin.Context) {
	var f models.OperationLog
	bind(c, &f)
	renderMessage(c, f.New())
}
