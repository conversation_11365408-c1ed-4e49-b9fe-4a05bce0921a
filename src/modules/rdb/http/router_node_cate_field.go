package http

import (
	"arboris/src/models"

	"github.com/gin-gonic/gin"
)

// @Summary 获取节点分类字段列表
// @Description 根据分类获取节点分类的自定义字段列表
// @Tags 节点分类字段管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param cate query string true "节点分类标识"
// @Success 200 {object} ApiResponse{dat=[]models.NodeCateField} "节点分类字段列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/node-cate-fields [get]
func nodeCateFieldGets(c *gin.Context) {
	lst, err := models.NodeCateFieldGets("cate = ?", queryStr(c, "cate"))
	renderData(c, lst, err)
}

// @Summary 获取节点分类字段详情
// @Description 获取指定节点分类字段的详细信息
// @Tags 节点分类字段管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "字段ID"
// @Success 200 {object} ApiResponse{dat=models.NodeCateField} "节点分类字段详情"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "字段不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/node-cate-field/{id} [get]
func nodeCateFieldGet(c *gin.Context) {
	obj, err := models.NodeCateFieldGet("id = ?", urlParamInt64(c, "id"))
	renderData(c, obj, err)
}

// @Summary 创建节点分类字段
// @Description 为节点分类创建新的自定义字段
// @Tags 节点分类字段管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param field body models.NodeCateField true "字段信息"
// @Success 200 {object} ApiResponse "创建成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/node-cate-fields [post]
func nodeCateFieldNew(c *gin.Context) {
	var obj models.NodeCateField
	bind(c, &obj)
	renderMessage(c, models.NodeCateFieldNew(&obj))
}

func nodeCateFieldPut(c *gin.Context) {
	var f models.NodeCateField
	bind(c, &f)

	obj, err := models.NodeCateFieldGet("id = ?", urlParamInt64(c, "id"))
	dangerous(err)

	if obj == nil {
		bomb("no such field")
	}

	if obj.FieldType != f.FieldType {
		bomb("field_type cannot modify")
	}

	obj.FieldName = f.FieldName
	obj.FieldExtra = f.FieldExtra
	obj.FieldRequired = f.FieldRequired

	renderMessage(c, obj.Update("field_name", "field_extra", "field_required"))
}

func nodeCateFieldDel(c *gin.Context) {
	obj, err := models.NodeCateFieldGet("id = ?", urlParamInt64(c, "id"))
	dangerous(err)

	if obj == nil {
		renderMessage(c, nil)
		return
	}

	renderMessage(c, obj.Del())
}
