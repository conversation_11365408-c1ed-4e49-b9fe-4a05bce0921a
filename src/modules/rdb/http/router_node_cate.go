package http

import (
	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/str"

	"arboris/src/models"
)

// @Summary 获取节点分类列表
// @Description 获取所有节点分类的列表
// @Tags 节点分类管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Success 200 {object} ApiResponse{dat=[]models.NodeCate} "节点分类列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/node-cates [get]
func nodeCateGets(c *gin.Context) {
	objs, err := models.NodeCateGets()
	renderData(c, objs, err)
}

type nodeCatePostForm struct {
	Ident     string `json:"ident"`
	Name      string `json:"name"`
	IconColor string `json:"icon_color"`
}

func (f nodeCatePostForm) Validate() {
	if f.Ident == "" {
		bomb("[%s] is blank", "ident")
	}

	if f.Name == "" {
		bomb("[%s] is blank", "name")
	}

	if len(f.Ident) > 32 {
		bomb("arg[%s] too long > %d", "ident", 32)
	}

	if len(f.Name) > 255 {
		bomb("arg[%s] too long > %d", "name", 255)
	}

	if !str.IsMatch(f.Ident, "[a-z]+") {
		bomb("arg[%s] invalid", "ident")
	}

	if str.Dangerous(f.Name) {
		bomb("arg[name] dangerous")
	}
}

// @Summary 创建节点分类
// @Description 创建新的节点分类
// @Tags 节点分类管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param nodeCate body nodeCatePostForm true "节点分类信息"
// @Success 200 {object} ApiResponse "创建成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/node-cates [post]
func nodeCatePost(c *gin.Context) {
	var f nodeCatePostForm
	bind(c, &f)
	f.Validate()

	obj := models.NodeCate{
		Ident:     f.Ident,
		Name:      f.Name,
		IconColor: f.IconColor,
	}

	renderMessage(c, models.NodeCateNew(&obj))
}

type nodeCatePutForm struct {
	Name      string `json:"name"`
	IconColor string `json:"icon_color"`
}

func (f nodeCatePutForm) Validate() {
	if len(f.Name) > 255 {
		bomb("arg[%s] too long > %d", "name", 255)
	}

	if str.Dangerous(f.Name) {
		bomb("arg[name] dangerous")
	}
}

// @Summary 修改节点分类
// @Description 修改指定节点分类的信息
// @Tags 节点分类管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "节点分类ID"
// @Param nodeCate body nodeCatePutForm true "节点分类信息"
// @Success 200 {object} ApiResponse "修改成功"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "节点分类不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/node-cate/{id} [put]
func nodeCatePut(c *gin.Context) {
	var f nodeCatePutForm
	bind(c, &f)
	f.Validate()

	id := urlParamInt64(c, "id")

	nc, err := models.NodeCateGet("id=?", id)
	dangerous(err)

	if nc == nil {
		bomb("no such NodeCate[id:%d]", id)
	}

	nc.Name = f.Name

	if nc.IconColor != f.IconColor {
		nc.IconColor = f.IconColor
		dangerous(models.UpdateIconColor(f.IconColor, nc.Ident))
	}

	renderMessage(c, nc.Update("name", "icon_color"))
}

// @Summary 删除节点分类
// @Description 删除指定的节点分类
// @Tags 节点分类管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "节点分类ID"
// @Success 200 {object} ApiResponse "删除成功"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "节点分类不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/node-cate/{id} [delete]
func nodeCateDel(c *gin.Context) {
	id := urlParamInt64(c, "id")

	nc, err := models.NodeCateGet("id=?", id)
	dangerous(err)

	if nc == nil {
		renderMessage(c, nil)
		return
	}

	renderMessage(c, nc.Del())
}
