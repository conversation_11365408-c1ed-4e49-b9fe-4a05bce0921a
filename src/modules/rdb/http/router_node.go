package http

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/str"

	"arboris/src/models"
	rdbEvents "arboris/src/modules/rdb/events"
)

// @Summary 获取节点详情
// @Description 获取指定节点的详细信息，包括管理员信息
// @Tags 节点管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "节点ID"
// @Success 200 {object} ApiResponse{dat=models.Node} "节点详情"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "节点不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/node/{id} [get]
func nodeGet(c *gin.Context) {
	node := Node(urlParamInt64(c, "id"))
	node.FillAdmins()
	renderData(c, node, nil)
}

// @Summary 获取节点信息(包含回收站)
// @Description 获取节点信息，包括已删除的节点，主要用于补全信息
// @Tags V1接口
// @Accept json
// @Produce json
// @Security ServiceAuth
// @Param id path int64 true "节点ID"
// @Success 200 {object} ApiResponse{dat=models.Node} "节点信息"
// @Failure 404 {object} ApiResponse "节点不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /v1/rdb/node-include-trash/{id} [get]
func nodeIncludeTrashGet(c *gin.Context) {
	nid := urlParamInt64(c, "id")
	realNode, err := models.NodeGet("id=?", nid)
	dangerous(err)
	if realNode != nil {
		realNode.FillAdmins()
		renderData(c, realNode, nil)
		return
	}

	var node *models.Node
	nodesInTrash, err := models.NodeTrashGetByIds([]int64{nid})
	dangerous(err)
	if len(nodesInTrash) == 1 {
		nodeInTrash := nodesInTrash[0]
		node = &models.Node{
			Id:          nid,
			Pid:         nodeInTrash.Pid,
			Ident:       nodeInTrash.Ident,
			Name:        nodeInTrash.Name,
			Note:        nodeInTrash.Note,
			Path:        nodeInTrash.Path,
			Leaf:        nodeInTrash.Leaf,
			Cate:        nodeInTrash.Cate,
			IconColor:   nodeInTrash.IconColor,
			IconChar:    nodeInTrash.IconChar,
			Proxy:       nodeInTrash.Proxy,
			Creator:     nodeInTrash.Creator,
			LastUpdated: nodeInTrash.LastUpdated,
		}
	}

	renderData(c, node, nil)
}

// @Summary 获取节点列表
// @Description 获取节点列表，支持按分类、内部节点、ID列表筛选
// @Tags 节点管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param cate query string false "节点分类"
// @Param inner query int false "是否包含内部节点，0-不包含，1-包含" default(0)
// @Param ids query string false "节点ID列表，逗号分隔"
// @Success 200 {object} ApiResponse{dat=[]models.Node} "节点列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/nodes [get]
func nodeGets(c *gin.Context) {
	cate := queryStr(c, "cate", "")
	withInner := queryInt(c, "inner", 0)
	ids := queryStr(c, "ids", "")

	where := ""
	param := []interface{}{}
	if cate != "" {
		where += "cate = ?"
		param = append(param, cate)
	}

	if withInner == 0 {
		if where != "" {
			where += " and "
		}
		where += "path not like ?"
		param = append(param, "inner")
	}

	if ids != "" {
		if where != "" {
			where += " and "
		}
		where += "id in (" + ids + ")"
	}

	nodes, err := models.NodeGets(where, param...)
	for i := 0; i < len(nodes); i++ {
		nodes[i].FillAdmins()
	}

	renderData(c, nodes, err)
}

type nodeForm struct {
	Pid      int64   `json:"pid"`
	Ident    string  `json:"ident"`
	Name     string  `json:"name"`
	Note     string  `json:"note"`
	Leaf     int     `json:"leaf"`
	Cate     string  `json:"cate"`
	Proxy    int     `json:"proxy"`
	AdminIds []int64 `json:"admin_ids"`
}

func (f nodeForm) Validate() {
	if f.Pid < 0 {
		bomb("arg[pid] invalid")
	}

	if !str.IsMatch(f.Ident, `^[a-z0-9\-_]+$`) {
		bomb("ident legal characters: [a-z0-9_-]")
	}

	if len(f.Ident) >= 32 {
		bomb("ident length should be less than 32")
	}

	if f.Leaf != 0 && f.Leaf != 1 {
		bomb("arg[leaf] invalid")
	}
}

// @Summary 创建节点
// @Description 创建新的节点，支持创建租户节点和普通节点
// @Tags 节点管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param node body nodeForm true "节点信息"
// @Success 200 {object} ApiResponse{dat=models.Node} "创建的节点信息"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/nodes [post]
func nodePost(c *gin.Context) {
	var f nodeForm
	bind(c, &f)
	f.Validate()

	me := loginUser(c)

	if f.Pid == 0 {
		// 只有超管才能创建租户
		if !me.IsRooter() {
			bomb("no privilege")
		}

		// 租户节点，租户节点已经设置为protected了，理论上不能被删除
		nc, err := models.NodeCateGet("ident=?", "tenant")
		dangerous(err)

		if nc == nil {
			bomb("node-category[tenant] not found")
		}

		node := &models.Node{
			Pid:       0,
			Ident:     f.Ident,
			Name:      f.Name,
			Path:      f.Ident,
			Leaf:      0,
			Cate:      "tenant",
			IconColor: nc.IconColor,
			IconChar:  "T",
			Proxy:     0,
			Note:      f.Note,
			Creator:   me.Username,
		}

		// 保存node到数据库
		dangerous(models.NodeNew(node, f.AdminIds))

		go models.OperationLogNew(me.Username, "node", node.Id, fmt.Sprintf("NodeCreate path: %s, name: %s", node.Path, node.Name))
		go publishNodeEvent(rdbEvents.EventNodeCreate, node)

		// 把节点详情返回，便于前端易用性处理
		renderData(c, node, nil)
	} else {
		// 非租户节点
		parent, err := models.NodeGet("id=?", f.Pid)
		dangerous(err)

		if parent == nil {
			bomb("arg[pid] invalid, no such parent node")
		}

		me.CheckPermByNode(parent, "rdb_node_create")

		if parent.Proxy > 0 {
			bomb("node is managed by other system")
		}

		child, err := parent.CreateChild(f.Ident, f.Name, f.Note, f.Cate, me.Username, f.Leaf, f.Proxy, f.AdminIds)
		if err == nil {
			go models.OperationLogNew(me.Username, "node", child.Id, fmt.Sprintf("NodeCreate path: %s, name: %s", child.Path, child.Name))
			go publishNodeEvent(rdbEvents.EventNodeCreate, child)
		}
		renderData(c, child, err)
	}
}

// @Summary 修改节点
// @Description 修改指定节点的信息，包括名称、分类、备注、管理员等
// @Tags 节点管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "节点ID"
// @Param node body nodeForm true "节点信息"
// @Success 200 {object} ApiResponse{dat=models.Node} "修改后的节点信息"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "节点不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/node/{id} [put]
func nodePut(c *gin.Context) {
	var f nodeForm
	bind(c, &f)

	id := urlParamInt64(c, "id")
	node := Node(id)

	me := loginUser(c)
	me.CheckPermByNode(node, "rdb_node_modify")

	// 即使是第三方系统创建的节点，也可以修改，只是改个名字、备注、类别、管理员，没啥大不了的
	// 第三方系统主要是管理下面的资源的挂载
	//if node.Proxy > 0 {
	//	bomb("node is managed by other system")
	//}

	if node.Cate == "tenant" && node.Cate != f.Cate {
		bomb("cannot modify tenant's node-category")
	}

	if node.Pid > 0 && f.Cate == "tenant" {
		bomb("cannot modify node-category to tenant")
	}

	// 保存原始名称，用于更新事件
	originalName := node.Name

	err := node.Modify(f.Name, f.Cate, f.Note, f.AdminIds)
	if err == nil {
		go models.OperationLogNew(me.Username, "node", node.Id, fmt.Sprintf("NodeModify path: %s, name: %s clientIP: %s", node.Path, node.Name, c.ClientIP()))
		go publishNodeUpdateEvent(node, originalName)
	}
	renderData(c, node, err)
}

// @Summary 删除节点
// @Description 删除指定的节点
// @Tags 节点管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Security CookieAuth
// @Param id path int64 true "节点ID"
// @Success 200 {object} ApiResponse "删除成功"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 403 {object} ApiResponse "权限不足"
// @Failure 404 {object} ApiResponse "节点不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /api/rdb/node/{id} [delete]
func nodeDel(c *gin.Context) {
	id := urlParamInt64(c, "id")
	node := Node(id)
	me := loginUser(c)
	me.CheckPermByNode(node, "rdb_node_delete")

	// 先发布删除事件，再删除节点
	go publishNodeEvent(rdbEvents.EventNodeDelete, node)
	dangerous(node.Del())
	go models.OperationLogNew(me.Username, "node", node.Id, fmt.Sprintf("NodeDelete path: %s, name: %s clientIP: %s", node.Path, node.Name, c.ClientIP()))
	renderMessage(c, nil)
}

type eventEntity struct {
	Action string      `json:"action"`
	Kind   string      `json:"kind"`
	Data   interface{} `json:"data"`
}

// publishNodeEvent 发布节点事件
func publishNodeEvent(eventType rdbEvents.EventType, node *models.Node) {
	nodeData := &rdbEvents.NodeEventData{
		ID:          node.Id,
		PID:         node.Pid,
		Ident:       node.Ident,
		Name:        node.Name,
		Note:        node.Note,
		Path:        node.Path,
		Leaf:        node.Leaf,
		Cate:        node.Cate,
		IconColor:   node.IconColor,
		IconChar:    node.IconChar,
		Proxy:       node.Proxy,
		Creator:     node.Creator,
		LastUpdated: node.LastUpdated.Unix(),
	}

	rdbEvents.PublishNodeEvent(eventType, nodeData)
}

// publishNodeUpdateEvent 发布节点更新事件（包含原始名称）
func publishNodeUpdateEvent(node *models.Node, originalName string) {
	nodeData := &rdbEvents.NodeEventData{
		ID:           node.Id,
		PID:          node.Pid,
		Ident:        node.Ident,
		Name:         node.Name,
		OriginalName: originalName, // 传递原始名称
		Note:         node.Note,
		Path:         node.Path,
		Leaf:         node.Leaf,
		Cate:         node.Cate,
		IconColor:    node.IconColor,
		IconChar:     node.IconChar,
		Proxy:        node.Proxy,
		Creator:      node.Creator,
		LastUpdated:  node.LastUpdated.Unix(),
	}

	rdbEvents.PublishNodeEvent(rdbEvents.EventNodeUpdate, nodeData)
}

//func nodeEvent(action string, node *models.Node) {
//	for _, backend := range config.Config.Webhook {
//		header := map[string]string{}
//		if backend.Token != "" {
//			header["Authorization"] = "Bearer " + backend.Token
//		}
//
//		event := &eventEntity{Action: action, Kind: "node", Data: node}
//		res, code, err := httplib.PostJSON(backend.Addr, 3*time.Second, event, header)
//		logger.Infof("Webhook, api:%s, event:%+v, error:%v, response:%s, statuscode:%d",
//			backend.Addr, event, err, string(res), code)
//	}
//}
