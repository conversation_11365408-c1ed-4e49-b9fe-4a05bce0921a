package config

import (
	"fmt"

	"github.com/toolkits/pkg/file"
)

// OpsStruct 权限配置结构数组
type OpsStruct []OpsSystem

// OpsSystem 权限配置系统结构
type OpsSystem struct {
	System string     `json:"system" description:"系统名称"`
	Groups []OpsGroup `json:"groups" description:"权限组列表"`
}

// OpsGroup 权限组结构
type OpsGroup struct {
	Title string `json:"title" description:"权限组标题"`
	Ops   []Op   `json:"ops" description:"权限操作列表"`
}

// Op 权限操作结构
type Op struct {
	En string `json:"en" description:"英文标识"`
	Cn string `json:"cn" description:"中文描述"`
}

var (
	GlobalOps    OpsStruct
	LocalOps     OpsStruct
	LocalOpsList []string
)

func parseOps() error {
	globalOpsFile := "etc/gop.local.yml"
	if !file.IsExist(globalOpsFile) {
		globalOpsFile = "etc/gop.yml"
	}

	if !file.IsExist(globalOpsFile) {
		return fmt.Errorf("%s not exists", globalOpsFile)
	}

	var gc OpsStruct
	err := file.ReadYaml(globalOpsFile, &gc)
	if err != nil {
		return fmt.Errorf("parse %s fail: %v", globalOpsFile, err)
	}

	GlobalOps = gc

	localOpsFile := "etc/lop.local.yml"
	if !file.IsExist(localOpsFile) {
		localOpsFile = "etc/lop.yml"
	}

	if !file.IsExist(localOpsFile) {
		return fmt.Errorf("%s not exists", localOpsFile)
	}

	var lc OpsStruct
	err = file.ReadYaml(localOpsFile, &lc)
	if err != nil {
		return fmt.Errorf("parse %s fail: %v", localOpsFile, err)
	}

	LocalOps = lc

	m := map[string]struct{}{}
	for _, v := range lc {
		for _, v2 := range v.Groups {
			for _, v3 := range v2.Ops {
				m[v3.En] = struct{}{}
			}
		}
	}
	LocalOpsList = []string{}
	for k, _ := range m {
		LocalOpsList = append(LocalOpsList, k)
	}

	return nil
}
