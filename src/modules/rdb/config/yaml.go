package config

import (
	"fmt"

	"github.com/toolkits/pkg/file"

	"arboris/src/common/loggeri"
	"arboris/src/toolkits/i18n"
)

type ConfigT struct {
	Logger loggeri.Config   `yaml:"logger"`
	HTTP   httpSection      `yaml:"http"`
	I18n   i18n.I18nSection `yaml:"i18n"`
	Auth   authSection      `yaml:"auth"`
	Redis  redisSection     `yaml:"redis"`
	Events eventsSection    `yaml:"events"`
}

type authSection struct {
	ExtraMode AuthExtraSection `yaml:"extraMode"`
}

type AuthExtraSection struct {
	Enable        bool   `yaml:"enable"`
	Debug         bool   `yaml:"debug" description:"debug"`
	DebugUser     string `yaml:"debugUser" description:"debug username"`
	WhiteList     bool   `yaml:"whiteList"`
	FrozenDays    int    `yaml:"frozenDays"`
	WritenOffDays int    `yaml:"writenOffDays"`
}

type httpSection struct {
	Mode    string         `yaml:"mode"`
	Session SessionSection `yaml:"session"`
}

type SessionSection struct {
	CookieName     string `yaml:"cookieName"`
	SidLength      int    `yaml:"sidLength"`
	HttpOnly       bool   `yaml:"httpOnly"`
	Domain         string `yaml:"domain"`
	GcInterval     int64  `yaml:"gcInterval"`
	CookieLifetime int64  `yaml:"cookieLifetime"`
	Storage        string `yaml:"storage" description:"mem|db(defualt)"`
}

type redisSection struct {
	Addr     string `yaml:"addr"`
	Password string `yaml:"password"`
	DB       int    `yaml:"db"`
}

type eventsSection struct {
	Enable     bool   `yaml:"enable"`
	StreamName string `yaml:"stream_name"`
}

var Config *ConfigT

// Parse configuration file
func Parse() error {
	ymlFile := getYmlFile()
	if ymlFile == "" {
		return fmt.Errorf("configuration file not found")
	}

	var c ConfigT
	err := file.ReadYaml(ymlFile, &c)
	if err != nil {
		return fmt.Errorf("cannot read yml[%s]: %v", ymlFile, err)
	}

	Config = &c
	fmt.Println("config.file:", ymlFile)

	if Config.I18n.DictPath == "" {
		Config.I18n.DictPath = "etc/dict.json"
	}

	if Config.I18n.Lang == "" {
		Config.I18n.Lang = "zh"
	}

	// 设置Redis默认值
	if Config.Redis.Addr == "" {
		Config.Redis.Addr = "127.0.0.1:6379"
	}
	if Config.Redis.DB == 0 {
		Config.Redis.DB = 0
	}

	// 设置Events默认值
	if Config.Events.StreamName == "" {
		Config.Events.StreamName = "arboris:sync:events"
	}

	if err = parseOps(); err != nil {
		return err
	}

	// if Config.HTTP.Session.CookieLifetime == 0 {
	// 	Config.HTTP.Session.CookieLifetime = 24 * 3600
	// }

	if Config.HTTP.Session.GcInterval == 0 {
		Config.HTTP.Session.GcInterval = 60
	}

	if Config.HTTP.Session.SidLength == 0 {
		Config.HTTP.Session.SidLength = 32
	}
	return nil
}

func getYmlFile() string {
	yml := "etc/rdb.local.yml"
	if file.IsExist(yml) {
		return yml
	}

	yml = "etc/rdb.yml"
	if file.IsExist(yml) {
		return yml
	}

	return ""
}
