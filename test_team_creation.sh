#!/bin/bash

# 测试团队创建功能的脚本

BASE_URL="http://localhost:8000"
TOKEN="41a7ae8949f77c3deb3a351bd6927734"

echo "=== 测试团队创建功能 ==="

# 1. 创建测试团队
echo "1. 创建测试团队..."
CREATE_RESPONSE=$(curl -s -X 'POST' \
  "${BASE_URL}/api/rdb/teams" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}" \
  -H 'Content-Type: application/json' \
  -d '{
  "ident": "test-team-001",
  "name": "测试团队001",
  "note": "这是一个测试团队",
  "mgmt": 0
}')

echo "创建团队响应: $CREATE_RESPONSE"

echo -e "\n等待3秒让团队创建事件处理完成..."
sleep 3

# 2. 获取团队ID
echo -e "\n2. 获取团队信息..."
TEAM_LIST=$(curl -s -X 'GET' \
  "${BASE_URL}/api/rdb/teams?limit=10&p=1" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}")

echo "团队列表: $TEAM_LIST"

# 简单提取团队ID（实际应该解析JSON）
TEAM_ID=$(echo "$TEAM_LIST" | grep -o '"id":[0-9]*' | tail -1 | cut -d':' -f2)
echo "团队ID: $TEAM_ID"

if [ -z "$TEAM_ID" ]; then
    echo "无法获取团队ID，退出测试"
    exit 1
fi

# 3. 更新团队信息
echo -e "\n3. 更新团队信息..."
UPDATE_RESPONSE=$(curl -s -X 'PUT' \
  "${BASE_URL}/api/rdb/team/${TEAM_ID}" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}" \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "更新后的测试团队001",
  "note": "这是一个更新后的测试团队",
  "mgmt": 1
}')

echo "更新团队响应: $UPDATE_RESPONSE"

echo -e "\n等待3秒让团队更新事件处理完成..."
sleep 3

# 4. 删除测试团队
echo -e "\n4. 删除测试团队..."
DELETE_RESPONSE=$(curl -s -X 'DELETE' \
  "${BASE_URL}/api/rdb/team/${TEAM_ID}" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}")

echo "删除团队响应: $DELETE_RESPONSE"

echo -e "\n等待3秒让团队删除事件处理完成..."
sleep 3

echo -e "\n=== 团队创建测试完成 ==="
echo "请检查jumpserver-sync日志确认团队事件是否正确处理"
