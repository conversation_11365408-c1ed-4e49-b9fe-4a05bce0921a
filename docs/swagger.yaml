basePath: /
definitions:
  config.Op:
    properties:
      cn:
        type: string
      en:
        type: string
    type: object
  config.OpsGroup:
    properties:
      ops:
        items:
          $ref: '#/definitions/config.Op'
        type: array
      title:
        type: string
    type: object
  config.OpsSystem:
    properties:
      groups:
        items:
          $ref: '#/definitions/config.OpsGroup'
        type: array
      system:
        type: string
    type: object
  http.ApiResponse:
    properties:
      dat: {}
      err:
        type: string
    type: object
  http.LoginLogListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/models.LoginLog'
        type: array
      total:
        type: integer
    type: object
  http.OperationLogListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/models.OperationLog'
        type: array
      total:
        type: integer
    type: object
  http.TeamDetailResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/models.User'
        type: array
      team:
        $ref: '#/definitions/models.Team'
      total:
        type: integer
    type: object
  http.TeamListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/models.Team'
        type: array
      total:
        type: integer
    type: object
  http.UserListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/models.User'
        type: array
      total:
        type: integer
    type: object
  http.WhiteListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/models.WhiteList'
        type: array
      total:
        type: integer
    type: object
  http.createWhiteListInput:
    properties:
      endIp:
        type: string
      endTime:
        type: integer
      startIp:
        type: string
      startTime:
        type: integer
    type: object
  http.loginInput:
    properties:
      args:
        items:
          type: string
        type: array
      password:
        type: string
      remote_addr:
        type: string
      type:
        type: string
      username:
        type: string
    type: object
  http.selfPasswordForm:
    properties:
      newpass:
        type: string
      oldpass:
        type: string
      username:
        type: string
    required:
    - newpass
    - oldpass
    - username
    type: object
  http.selfProfileForm:
    properties:
      dispname:
        type: string
      email:
        type: string
      im:
        type: string
      intro:
        type: string
      phone:
        type: string
      portrait:
        type: string
    type: object
  http.selfTokenForm:
    properties:
      token:
        type: string
    type: object
  http.teamForm:
    properties:
      ident:
        type: string
      mgmt:
        type: integer
      name:
        type: string
      note:
        type: string
    type: object
  http.teamUserBindForm:
    properties:
      admin_ids:
        items:
          type: integer
        type: array
      member_ids:
        items:
          type: integer
        type: array
    type: object
  http.teamUserUnbindForm:
    properties:
      user_ids:
        items:
          type: integer
        type: array
    type: object
  http.updateWhiteListInput:
    properties:
      endIp:
        type: string
      endTime:
        type: integer
      startIp:
        type: string
      startTime:
        type: integer
    type: object
  http.userInviteForm:
    properties:
      dispname:
        type: string
      email:
        type: string
      im:
        type: string
      password:
        type: string
      phone:
        type: string
      token:
        type: string
      username:
        type: string
    required:
    - password
    - token
    - username
    type: object
  http.userPasswordForm:
    properties:
      password:
        type: string
    required:
    - password
    type: object
  http.userProfileForm:
    properties:
      dispname:
        type: string
      email:
        type: string
      im:
        type: string
      is_root:
        type: integer
      leader_id:
        type: integer
      organization:
        type: string
      password:
        type: string
      phone:
        type: string
      status:
        type: integer
      typ:
        type: integer
      username:
        type: string
    type: object
  models.AuthConfig:
    properties:
      lockTime:
        type: integer
      maxConnIdleTime:
        type: integer
      maxNumErr:
        type: integer
      maxSessionNumber:
        type: integer
      pwdExpiresIn:
        type: integer
      pwdHistorySize:
        type: integer
      pwdMinLenght:
        type: integer
      pwdMustInclude:
        items:
          type: string
        type: array
      pwdMustIncludeFlag:
        type: integer
    type: object
  models.LoginLog:
    properties:
      client:
        type: string
      clock:
        type: integer
      err:
        type: string
      id:
        type: integer
      loginout:
        type: string
      username:
        type: string
    type: object
  models.OperationLog:
    properties:
      clock:
        type: integer
      detail:
        type: string
      id:
        type: integer
      res_cl:
        type: string
      res_id:
        type: string
      username:
        type: string
    type: object
  models.Team:
    properties:
      creator:
        type: integer
      id:
        type: integer
      ident:
        type: string
      last_updated:
        type: string
      mgmt:
        type: integer
      name:
        type: string
      note:
        type: string
    type: object
  models.User:
    properties:
      active_begin:
        type: integer
      active_end:
        type: integer
      create_at:
        type: string
      dispname:
        type: string
      email:
        type: string
      id:
        type: integer
      im:
        type: string
      intro:
        type: string
      is_root:
        type: integer
      leader_id:
        type: integer
      leader_name:
        type: string
      locked_at:
        type: integer
      logged_at:
        type: integer
      login_err_num:
        type: integer
      organization:
        type: string
      phone:
        type: string
      portrait:
        type: string
      pwd_expires_at:
        type: integer
      pwd_updated_at:
        type: integer
      status:
        type: integer
      type:
        type: integer
      updated_at:
        type: integer
      username:
        type: string
      uuid:
        type: string
    type: object
  models.UserToken:
    properties:
      token:
        type: string
      user_id:
        type: integer
      username:
        type: string
    type: object
  models.WhiteList:
    properties:
      createdAt:
        type: integer
      creator:
        type: string
      endIp:
        type: string
      endTime:
        type: integer
      id:
        type: integer
      startIp:
        type: string
      startTime:
        type: integer
      updateAt:
        type: integer
      updater:
        type: string
    type: object
host: localhost:8000
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: Arboris RDB 模块 API 文档
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Arboris RDB API
  version: "1.0"
paths:
  /api/rdb/auth/login:
    post:
      consumes:
      - application/json
      description: 用户通过用户名和密码登录系统
      parameters:
      - description: 登录信息
        in: body
        name: login
        required: true
        schema:
          $ref: '#/definitions/http.loginInput'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 认证失败
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      summary: 用户登录
      tags:
      - 认证管理
  /api/rdb/auth/logout:
    get:
      consumes:
      - application/json
      description: 用户登出系统，清除会话信息
      produces:
      - application/json
      responses:
        "200":
          description: 登出成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - CookieAuth: []
      summary: 用户登出
      tags:
      - 认证管理
  /api/rdb/auth/white-list:
    get:
      consumes:
      - application/json
      description: 获取IP白名单列表，支持分页和搜索
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      - description: 搜索关键字
        in: query
        name: query
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 白名单列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.WhiteListResponse'
              type: object
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取白名单列表
      tags:
      - 白名单管理
    post:
      consumes:
      - application/json
      description: 创建新的IP白名单条目，用于访问控制
      parameters:
      - description: 白名单信息
        in: body
        name: whitelist
        required: true
        schema:
          $ref: '#/definitions/http.createWhiteListInput'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功，返回ID
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  additionalProperties: true
                  type: object
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 创建白名单条目
      tags:
      - 白名单管理
  /api/rdb/auth/white-list/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的白名单条目
      parameters:
      - description: 白名单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 白名单不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 删除白名单条目
      tags:
      - 白名单管理
    get:
      consumes:
      - application/json
      description: 根据ID获取指定的白名单条目详情
      parameters:
      - description: 白名单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 白名单详情
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.WhiteList'
              type: object
        "404":
          description: 白名单不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取单个白名单条目
      tags:
      - 白名单管理
    put:
      consumes:
      - application/json
      description: 更新指定ID的白名单条目信息
      parameters:
      - description: 白名单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 更新的白名单信息
        in: body
        name: whitelist
        required: true
        schema:
          $ref: '#/definitions/http.updateWhiteListInput'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 白名单不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 更新白名单条目
      tags:
      - 白名单管理
  /api/rdb/configs/auth:
    get:
      consumes:
      - application/json
      description: 获取系统认证配置信息，包括密码策略、会话管理等设置
      produces:
      - application/json
      responses:
        "200":
          description: 认证配置信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.AuthConfig'
              type: object
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取认证配置
      tags:
      - 认证配置
    put:
      consumes:
      - application/json
      description: 更新系统认证配置信息，包括密码策略、会话管理等设置
      parameters:
      - description: 认证配置信息
        in: body
        name: config
        required: true
        schema:
          $ref: '#/definitions/models.AuthConfig'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 更新认证配置
      tags:
      - 认证配置
  /api/rdb/log/login:
    get:
      consumes:
      - application/json
      description: 超级管理员查看所有用户的登录记录
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      - description: 用户名
        in: query
        name: username
        type: string
      - description: 开始时间戳
        in: query
        name: btime
        type: integer
      - description: 结束时间戳
        in: query
        name: etime
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 登录日志列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.LoginLogListResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取登录日志
      tags:
      - 日志管理
  /api/rdb/log/operation:
    get:
      consumes:
      - application/json
      description: 超级管理员查看所有类型资源的操作记录
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      - description: 搜索关键字
        in: query
        name: query
        type: string
      - description: 开始时间戳
        in: query
        name: btime
        type: integer
      - description: 结束时间戳
        in: query
        name: etime
        type: integer
      - description: 用户名
        in: query
        name: username
        type: string
      - description: 资源类型
        in: query
        name: res_type
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 操作日志列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.OperationLogListResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取操作日志
      tags:
      - 日志管理
  /api/rdb/ops/global:
    get:
      consumes:
      - application/json
      description: 获取系统定义的页面权限配置
      produces:
      - application/json
      responses:
        "200":
          description: 全局权限配置
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/config.OpsSystem'
                  type: array
              type: object
      summary: 获取全局权限配置
      tags:
      - 系统管理
  /api/rdb/ops/local:
    get:
      consumes:
      - application/json
      description: 获取系统定义的资源权限配置
      produces:
      - application/json
      responses:
        "200":
          description: 本地权限配置
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/config.OpsSystem'
                  type: array
              type: object
      summary: 获取本地权限配置
      tags:
      - 系统管理
  /api/rdb/ping:
    get:
      consumes:
      - application/json
      description: 检查服务是否正常运行
      produces:
      - text/plain
      responses:
        "200":
          description: pong
          schema:
            type: string
      summary: 健康检查
      tags:
      - 系统管理
  /api/rdb/pwd-rules:
    get:
      consumes:
      - application/json
      description: 获取系统密码策略规则，用于前端显示密码要求
      produces:
      - application/json
      responses:
        "200":
          description: 密码规则列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    type: string
                  type: array
              type: object
      summary: 获取密码规则
      tags:
      - 系统管理
  /api/rdb/root/teams/all:
    get:
      consumes:
      - application/json
      description: 超级管理员获取系统中所有团队的列表，支持分页和搜索
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      - description: 搜索关键字
        in: query
        name: query
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 团队列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.TeamListResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取所有团队列表(超管)
      tags:
      - 团队管理
  /api/rdb/self/password:
    put:
      consumes:
      - application/json
      description: 用户修改自己的登录密码
      parameters:
      - description: 密码修改信息
        in: body
        name: password
        required: true
        schema:
          $ref: '#/definitions/http.selfPasswordForm'
      produces:
      - application/json
      responses:
        "200":
          description: 密码修改成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 修改个人密码
      tags:
      - 个人中心
  /api/rdb/self/profile:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的个人信息
      produces:
      - application/json
      responses:
        "200":
          description: 个人信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.User'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取个人信息
      tags:
      - 个人中心
    put:
      consumes:
      - application/json
      description: 更新当前登录用户的个人信息
      parameters:
      - description: 个人信息
        in: body
        name: profile
        required: true
        schema:
          $ref: '#/definitions/http.selfProfileForm'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 更新个人信息
      tags:
      - 个人中心
  /api/rdb/self/token:
    get:
      consumes:
      - application/json
      description: 获取当前用户的所有API Token
      produces:
      - application/json
      responses:
        "200":
          description: Token列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.UserToken'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取个人Token列表
      tags:
      - 个人中心
    post:
      consumes:
      - application/json
      description: 为当前用户创建新的API Token
      produces:
      - application/json
      responses:
        "200":
          description: 新创建的Token
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.UserToken'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 创建个人Token
      tags:
      - 个人中心
    put:
      consumes:
      - application/json
      description: 重置指定的API Token
      parameters:
      - description: Token信息
        in: body
        name: token
        required: true
        schema:
          $ref: '#/definitions/http.selfTokenForm'
      produces:
      - application/json
      responses:
        "200":
          description: 重置后的Token
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.UserToken'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 重置个人Token
      tags:
      - 个人中心
  /api/rdb/team/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定的团队
      parameters:
      - description: 团队ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 团队不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 删除团队
      tags:
      - 团队管理
    get:
      consumes:
      - application/json
      description: 获取指定团队的详细信息，包括团队成员列表
      parameters:
      - description: 团队ID
        in: path
        name: id
        required: true
        type: integer
      - description: 搜索关键字
        in: query
        name: query
        type: string
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 团队详情和成员列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.TeamDetailResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 团队不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取团队详情
      tags:
      - 团队管理
    put:
      consumes:
      - application/json
      description: 更新指定团队的信息
      parameters:
      - description: 团队ID
        in: path
        name: id
        required: true
        type: integer
      - description: 团队信息
        in: body
        name: team
        required: true
        schema:
          $ref: '#/definitions/http.teamForm'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 团队不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 更新团队信息
      tags:
      - 团队管理
  /api/rdb/team/{id}/users/bind:
    put:
      consumes:
      - application/json
      description: 为团队添加管理员和普通成员
      parameters:
      - description: 团队ID
        in: path
        name: id
        required: true
        type: integer
      - description: 用户绑定信息
        in: body
        name: users
        required: true
        schema:
          $ref: '#/definitions/http.teamUserBindForm'
      produces:
      - application/json
      responses:
        "200":
          description: 绑定成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 团队不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 绑定团队成员
      tags:
      - 团队管理
  /api/rdb/team/{id}/users/unbind:
    put:
      consumes:
      - application/json
      description: 从团队中移除指定的成员
      parameters:
      - description: 团队ID
        in: path
        name: id
        required: true
        type: integer
      - description: 用户解绑信息
        in: body
        name: users
        required: true
        schema:
          $ref: '#/definitions/http.teamUserUnbindForm'
      produces:
      - application/json
      responses:
        "200":
          description: 解绑成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 团队不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 解绑团队成员
      tags:
      - 团队管理
  /api/rdb/teams:
    post:
      consumes:
      - application/json
      description: 创建新的团队
      parameters:
      - description: 团队信息
        in: body
        name: team
        required: true
        schema:
          $ref: '#/definitions/http.teamForm'
      produces:
      - application/json
      responses:
        "200":
          description: 创建的团队信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.Team'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 创建团队
      tags:
      - 团队管理
  /api/rdb/teams/all:
    get:
      consumes:
      - application/json
      description: 获取系统中所有团队的列表，支持分页和搜索
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      - description: 搜索关键字
        in: query
        name: query
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 团队列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.TeamListResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取所有团队列表
      tags:
      - 团队管理
  /api/rdb/teams/mine:
    get:
      consumes:
      - application/json
      description: 获取当前用户所属的团队列表，支持分页和搜索
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      - description: 搜索关键字
        in: query
        name: query
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 我的团队列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.TeamListResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取我的团队列表
      tags:
      - 团队管理
  /api/rdb/user/{id}:
    delete:
      consumes:
      - application/json
      description: 超级管理员删除指定用户
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 删除用户
      tags:
      - 用户管理
  /api/rdb/user/{id}/password:
    put:
      consumes:
      - application/json
      description: 超级管理员重置指定用户的密码
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      - description: 新密码
        in: body
        name: password
        required: true
        schema:
          $ref: '#/definitions/http.userPasswordForm'
      produces:
      - application/json
      responses:
        "200":
          description: 密码重置成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 重置用户密码
      tags:
      - 用户管理
  /api/rdb/user/{id}/profile:
    get:
      consumes:
      - application/json
      description: 超级管理员获取指定用户的详细信息
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 用户详情
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.User'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取用户详情
      tags:
      - 用户管理
    put:
      consumes:
      - application/json
      description: 超级管理员更新指定用户的信息
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      - description: 用户信息
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/http.userProfileForm'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 更新用户信息
      tags:
      - 用户管理
  /api/rdb/users:
    get:
      consumes:
      - application/json
      description: 获取系统用户列表，支持分页和搜索
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      - description: 搜索关键字
        in: query
        name: query
        type: string
      - description: 查询条件
        in: query
        name: conditions
        type: string
      - description: 组织
        in: query
        name: org
        type: string
      - description: 用户ID列表，逗号分隔
        in: query
        name: ids
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 用户列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.UserListResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取用户列表
      tags:
      - 用户管理
    post:
      consumes:
      - application/json
      description: 超级管理员创建新用户
      parameters:
      - description: 用户信息
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/http.userProfileForm'
      produces:
      - application/json
      responses:
        "200":
          description: 创建的用户信息
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 创建用户
      tags:
      - 用户管理
  /api/rdb/users/invite:
    get:
      consumes:
      - application/json
      description: 生成用户注册邀请Token，用于用户自助注册
      produces:
      - application/json
      responses:
        "200":
          description: 邀请Token
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  type: string
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 生成用户邀请Token
      tags:
      - 用户管理
    post:
      consumes:
      - application/json
      description: 使用邀请Token进行用户自助注册
      parameters:
      - description: 用户注册信息
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/http.userInviteForm'
      produces:
      - application/json
      responses:
        "200":
          description: 注册成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      summary: 通过邀请Token注册用户
      tags:
      - 用户管理
securityDefinitions:
  ApiKeyAuth:
    description: User token for API authentication
    in: header
    name: X-User-Token
    type: apiKey
  CookieAuth:
    description: Session cookie for web authentication
    in: cookie
    name: ecmc-sid
    type: apiKey
swagger: "2.0"
