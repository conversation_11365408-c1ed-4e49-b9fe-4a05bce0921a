// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/rdb/auth/login": {
            "post": {
                "description": "用户通过用户名和密码登录系统",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证管理"
                ],
                "summary": "用户登录",
                "parameters": [
                    {
                        "description": "登录信息",
                        "name": "login",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.loginInput"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "认证失败",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/auth/logout": {
            "get": {
                "security": [
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "用户登出系统，清除会话信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证管理"
                ],
                "summary": "用户登出",
                "responses": {
                    "200": {
                        "description": "登出成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/auth/white-list": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取IP白名单列表，支持分页和搜索",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "白名单管理"
                ],
                "summary": "获取白名单列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "query",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "白名单列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.WhiteListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "创建新的IP白名单条目，用于访问控制",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "白名单管理"
                ],
                "summary": "创建白名单条目",
                "parameters": [
                    {
                        "description": "白名单信息",
                        "name": "whitelist",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.createWhiteListInput"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功，返回ID",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "object",
                                            "additionalProperties": true
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/auth/white-list/{id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "根据ID获取指定的白名单条目详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "白名单管理"
                ],
                "summary": "获取单个白名单条目",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "白名单ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "白名单详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.WhiteList"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "白名单不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "更新指定ID的白名单条目信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "白名单管理"
                ],
                "summary": "更新白名单条目",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "白名单ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "更新的白名单信息",
                        "name": "whitelist",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.updateWhiteListInput"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "白名单不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "删除指定ID的白名单条目",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "白名单管理"
                ],
                "summary": "删除白名单条目",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "白名单ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "白名单不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/configs/auth": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取系统认证配置信息，包括密码策略、会话管理等设置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证配置"
                ],
                "summary": "获取认证配置",
                "responses": {
                    "200": {
                        "description": "认证配置信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.AuthConfig"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "更新系统认证配置信息，包括密码策略、会话管理等设置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证配置"
                ],
                "summary": "更新认证配置",
                "parameters": [
                    {
                        "description": "认证配置信息",
                        "name": "config",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AuthConfig"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/log/login": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员查看所有用户的登录记录",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "日志管理"
                ],
                "summary": "获取登录日志",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "开始时间戳",
                        "name": "btime",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "结束时间戳",
                        "name": "etime",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录日志列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.LoginLogListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/log/operation": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员查看所有类型资源的操作记录",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "日志管理"
                ],
                "summary": "获取操作日志",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "query",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "开始时间戳",
                        "name": "btime",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "结束时间戳",
                        "name": "etime",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "res_type",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "操作日志列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.OperationLogListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/ops/global": {
            "get": {
                "description": "获取系统定义的页面权限配置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "系统管理"
                ],
                "summary": "获取全局权限配置",
                "responses": {
                    "200": {
                        "description": "全局权限配置",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/config.OpsSystem"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/rdb/ops/local": {
            "get": {
                "description": "获取系统定义的资源权限配置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "系统管理"
                ],
                "summary": "获取本地权限配置",
                "responses": {
                    "200": {
                        "description": "本地权限配置",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/config.OpsSystem"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/rdb/ping": {
            "get": {
                "description": "检查服务是否正常运行",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "text/plain"
                ],
                "tags": [
                    "系统管理"
                ],
                "summary": "健康检查",
                "responses": {
                    "200": {
                        "description": "pong",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/rdb/pwd-rules": {
            "get": {
                "description": "获取系统密码策略规则，用于前端显示密码要求",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "系统管理"
                ],
                "summary": "获取密码规则",
                "responses": {
                    "200": {
                        "description": "密码规则列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "type": "string"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/rdb/root/teams/all": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员获取系统中所有团队的列表，支持分页和搜索",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "获取所有团队列表(超管)",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "query",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "团队列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.TeamListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/self/password": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "用户修改自己的登录密码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "个人中心"
                ],
                "summary": "修改个人密码",
                "parameters": [
                    {
                        "description": "密码修改信息",
                        "name": "password",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.selfPasswordForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "密码修改成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/self/profile": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取当前登录用户的个人信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "个人中心"
                ],
                "summary": "获取个人信息",
                "responses": {
                    "200": {
                        "description": "个人信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.User"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "更新当前登录用户的个人信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "个人中心"
                ],
                "summary": "更新个人信息",
                "parameters": [
                    {
                        "description": "个人信息",
                        "name": "profile",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.selfProfileForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/self/token": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取当前用户的所有API Token",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "个人中心"
                ],
                "summary": "获取个人Token列表",
                "responses": {
                    "200": {
                        "description": "Token列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.UserToken"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "重置指定的API Token",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "个人中心"
                ],
                "summary": "重置个人Token",
                "parameters": [
                    {
                        "description": "Token信息",
                        "name": "token",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.selfTokenForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "重置后的Token",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.UserToken"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "为当前用户创建新的API Token",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "个人中心"
                ],
                "summary": "创建个人Token",
                "responses": {
                    "200": {
                        "description": "新创建的Token",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.UserToken"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/team/{id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取指定团队的详细信息，包括团队成员列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "获取团队详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "团队ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "query",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "团队详情和成员列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.TeamDetailResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "团队不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "更新指定团队的信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "更新团队信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "团队ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "团队信息",
                        "name": "team",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.teamForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "团队不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "删除指定的团队",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "删除团队",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "团队ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "团队不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/team/{id}/users/bind": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "为团队添加管理员和普通成员",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "绑定团队成员",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "团队ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "用户绑定信息",
                        "name": "users",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.teamUserBindForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "绑定成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "团队不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/team/{id}/users/unbind": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "从团队中移除指定的成员",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "解绑团队成员",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "团队ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "用户解绑信息",
                        "name": "users",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.teamUserUnbindForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "解绑成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "团队不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/teams": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "创建新的团队",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "创建团队",
                "parameters": [
                    {
                        "description": "团队信息",
                        "name": "team",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.teamForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建的团队信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.Team"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/teams/all": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取系统中所有团队的列表，支持分页和搜索",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "获取所有团队列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "query",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "团队列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.TeamListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/teams/mine": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取当前用户所属的团队列表，支持分页和搜索",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "团队管理"
                ],
                "summary": "获取我的团队列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "query",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "我的团队列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.TeamListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/user/{id}": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员删除指定用户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "删除用户",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/user/{id}/password": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员重置指定用户的密码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "重置用户密码",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "新密码",
                        "name": "password",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.userPasswordForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "密码重置成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/user/{id}/profile": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员获取指定用户的详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "获取用户详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用户详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.User"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员更新指定用户的信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "更新用户信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "用户信息",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.userProfileForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/users": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取系统用户列表，支持分页和搜索",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "获取用户列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "query",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "查询条件",
                        "name": "conditions",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "组织",
                        "name": "org",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户ID列表，逗号分隔",
                        "name": "ids",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用户列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.UserListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "超级管理员创建新用户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "创建用户",
                "parameters": [
                    {
                        "description": "用户信息",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.userProfileForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建的用户信息",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/rdb/users/invite": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "生成用户注册邀请Token，用于用户自助注册",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "生成用户邀请Token",
                "responses": {
                    "200": {
                        "description": "邀请Token",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "使用邀请Token进行用户自助注册",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "通过邀请Token注册用户",
                "parameters": [
                    {
                        "description": "用户注册信息",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.userInviteForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "注册成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "config.Op": {
            "type": "object",
            "properties": {
                "cn": {
                    "type": "string"
                },
                "en": {
                    "type": "string"
                }
            }
        },
        "config.OpsGroup": {
            "type": "object",
            "properties": {
                "ops": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/config.Op"
                    }
                },
                "title": {
                    "type": "string"
                }
            }
        },
        "config.OpsSystem": {
            "type": "object",
            "properties": {
                "groups": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/config.OpsGroup"
                    }
                },
                "system": {
                    "type": "string"
                }
            }
        },
        "http.ApiResponse": {
            "type": "object",
            "properties": {
                "dat": {},
                "err": {
                    "type": "string"
                }
            }
        },
        "http.LoginLogListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.LoginLog"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "http.OperationLogListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.OperationLog"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "http.TeamDetailResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.User"
                    }
                },
                "team": {
                    "$ref": "#/definitions/models.Team"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "http.TeamListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Team"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "http.UserListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.User"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "http.WhiteListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.WhiteList"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "http.createWhiteListInput": {
            "type": "object",
            "properties": {
                "endIp": {
                    "type": "string"
                },
                "endTime": {
                    "type": "integer"
                },
                "startIp": {
                    "type": "string"
                },
                "startTime": {
                    "type": "integer"
                }
            }
        },
        "http.loginInput": {
            "type": "object",
            "properties": {
                "args": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "password": {
                    "type": "string"
                },
                "remote_addr": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "http.selfPasswordForm": {
            "type": "object",
            "required": [
                "newpass",
                "oldpass",
                "username"
            ],
            "properties": {
                "newpass": {
                    "type": "string"
                },
                "oldpass": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "http.selfProfileForm": {
            "type": "object",
            "properties": {
                "dispname": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "im": {
                    "type": "string"
                },
                "intro": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "portrait": {
                    "type": "string"
                }
            }
        },
        "http.selfTokenForm": {
            "type": "object",
            "properties": {
                "token": {
                    "type": "string"
                }
            }
        },
        "http.teamForm": {
            "type": "object",
            "properties": {
                "ident": {
                    "type": "string"
                },
                "mgmt": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "note": {
                    "type": "string"
                }
            }
        },
        "http.teamUserBindForm": {
            "type": "object",
            "properties": {
                "admin_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "member_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "http.teamUserUnbindForm": {
            "type": "object",
            "properties": {
                "user_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "http.updateWhiteListInput": {
            "type": "object",
            "properties": {
                "endIp": {
                    "type": "string"
                },
                "endTime": {
                    "type": "integer"
                },
                "startIp": {
                    "type": "string"
                },
                "startTime": {
                    "type": "integer"
                }
            }
        },
        "http.userInviteForm": {
            "type": "object",
            "required": [
                "password",
                "token",
                "username"
            ],
            "properties": {
                "dispname": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "im": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "token": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "http.userPasswordForm": {
            "type": "object",
            "required": [
                "password"
            ],
            "properties": {
                "password": {
                    "type": "string"
                }
            }
        },
        "http.userProfileForm": {
            "type": "object",
            "properties": {
                "dispname": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "im": {
                    "type": "string"
                },
                "is_root": {
                    "type": "integer"
                },
                "leader_id": {
                    "type": "integer"
                },
                "organization": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "status": {
                    "type": "integer"
                },
                "typ": {
                    "type": "integer"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "models.AuthConfig": {
            "type": "object",
            "properties": {
                "lockTime": {
                    "type": "integer"
                },
                "maxConnIdleTime": {
                    "type": "integer"
                },
                "maxNumErr": {
                    "type": "integer"
                },
                "maxSessionNumber": {
                    "type": "integer"
                },
                "pwdExpiresIn": {
                    "type": "integer"
                },
                "pwdHistorySize": {
                    "type": "integer"
                },
                "pwdMinLenght": {
                    "type": "integer"
                },
                "pwdMustInclude": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "pwdMustIncludeFlag": {
                    "type": "integer"
                }
            }
        },
        "models.LoginLog": {
            "type": "object",
            "properties": {
                "client": {
                    "type": "string"
                },
                "clock": {
                    "type": "integer"
                },
                "err": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "loginout": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "models.OperationLog": {
            "type": "object",
            "properties": {
                "clock": {
                    "type": "integer"
                },
                "detail": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "res_cl": {
                    "type": "string"
                },
                "res_id": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "models.Team": {
            "type": "object",
            "properties": {
                "creator": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "ident": {
                    "type": "string"
                },
                "last_updated": {
                    "type": "string"
                },
                "mgmt": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "note": {
                    "type": "string"
                }
            }
        },
        "models.User": {
            "type": "object",
            "properties": {
                "active_begin": {
                    "type": "integer"
                },
                "active_end": {
                    "type": "integer"
                },
                "create_at": {
                    "type": "string"
                },
                "dispname": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "im": {
                    "type": "string"
                },
                "intro": {
                    "type": "string"
                },
                "is_root": {
                    "type": "integer"
                },
                "leader_id": {
                    "type": "integer"
                },
                "leader_name": {
                    "type": "string"
                },
                "locked_at": {
                    "type": "integer"
                },
                "logged_at": {
                    "type": "integer"
                },
                "login_err_num": {
                    "type": "integer"
                },
                "organization": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "portrait": {
                    "type": "string"
                },
                "pwd_expires_at": {
                    "type": "integer"
                },
                "pwd_updated_at": {
                    "type": "integer"
                },
                "status": {
                    "type": "integer"
                },
                "type": {
                    "type": "integer"
                },
                "updated_at": {
                    "type": "integer"
                },
                "username": {
                    "type": "string"
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "models.UserToken": {
            "type": "object",
            "properties": {
                "token": {
                    "type": "string"
                },
                "user_id": {
                    "type": "integer"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "models.WhiteList": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "type": "integer"
                },
                "creator": {
                    "type": "string"
                },
                "endIp": {
                    "type": "string"
                },
                "endTime": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "startIp": {
                    "type": "string"
                },
                "startTime": {
                    "type": "integer"
                },
                "updateAt": {
                    "type": "integer"
                },
                "updater": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "description": "User token for API authentication",
            "type": "apiKey",
            "name": "X-User-Token",
            "in": "header"
        },
        "CookieAuth": {
            "description": "Session cookie for web authentication",
            "type": "apiKey",
            "name": "ecmc-sid",
            "in": "cookie"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8000",
	BasePath:         "/",
	Schemes:          []string{},
	Title:            "Arboris RDB API",
	Description:      "Arboris RDB 模块 API 文档",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
