// Package docs_ams Code generated by swaggo/swag. DO NOT EDIT
package docs_ams

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/ams-ce/host": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "添加单台主机设备，支持基础字段和自定义字段",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "添加主机",
                "parameters": [
                    {
                        "description": "主机添加参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.hostAddForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建的主机信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/arboris_src_models.Host"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "409": {
                        "description": "IP或标识符已存在",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/ams-ce/host/filter": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "根据基础字段和自定义字段多维度过滤主机列表，支持分页，返回结果包含节点路径信息和自定义字段值",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "多维度过滤主机",
                "parameters": [
                    {
                        "description": "过滤条件",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.hostFilterForm"
                        }
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "过滤后的主机列表，包含节点路径和自定义字段值",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/src_modules_ams_http.HostFilterResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/ams-ce/host/{id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "根据主机ID获取主机详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "获取单个主机信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "主机ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "主机信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/arboris_src_models.Host"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "主机不存在",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/ams-ce/host/{id}/fields": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取指定主机的所有字段值",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机字段管理"
                ],
                "summary": "获取主机字段值",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "主机ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "主机字段值列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/arboris_src_models.HostFieldValue"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "主机不存在",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "批量更新指定主机的字段值",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机字段管理"
                ],
                "summary": "更新主机字段值",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "主机ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "主机字段值列表",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/arboris_src_models.HostFieldValue"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "主机不存在",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/ams-ce/hosts": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取主机列表，支持分页、搜索和批量筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "获取主机列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "租户",
                        "name": "tenant",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "query",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "批量IP列表，逗号分隔",
                        "name": "batch",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "default": "ip",
                        "description": "搜索字段",
                        "name": "field",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "主机列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/src_modules_ams_http.HostListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "批量删除主机设备，支持通过ID或IP删除",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "删除主机",
                "parameters": [
                    {
                        "description": "主机删除参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.idsOrIpsForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/ams-ce/hosts/cate": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "批量修改主机设备的类别信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "修改主机类别",
                "parameters": [
                    {
                        "description": "主机类别修改参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.hostCateForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改成功",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/ams-ce/hosts/csv/failed/{import_id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "根据导入批次ID下载导入失败的CSV数据，包含原始数据和错误信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/octet-stream"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "下载导入失败的数据",
                "parameters": [
                    {
                        "type": "string",
                        "description": "导入批次ID",
                        "name": "import_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "失败数据CSV文件",
                        "schema": {
                            "type": "file"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "导入批次不存在或已过期",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/ams-ce/hosts/csv/import": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "通过CSV或Excel文件批量导入主机数据，支持基础字段和自定义字段。支持.csv、.xls、.xlsx格式。最大支持1000行数据导入。返回导入结果统计信息，详细错误信息可通过失败数据下载接口获取",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "批量导入主机",
                "parameters": [
                    {
                        "type": "file",
                        "description": "CSV或Excel文件（支持.csv、.xls、.xlsx格式，最大1000行数据，不包括标题行）",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "导入完成，返回统计信息。如果有失败记录，err字段会包含错误提示，import_id用于下载失败数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/src_modules_ams_http.ImportResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误（如文件格式错误、超过1000行限制等）",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/ams-ce/hosts/csv/template": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "动态根据自定义字段生成主机导入模板文件，支持CSV和Excel格式",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/octet-stream"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "下载主机导入模板",
                "parameters": [
                    {
                        "enum": [
                            "csv",
                            "xlsx"
                        ],
                        "type": "string",
                        "default": "csv",
                        "description": "文件格式",
                        "name": "format",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "模板文件",
                        "schema": {
                            "type": "file"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/ams-ce/hosts/field/{id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "根据字段ID获取主机字段定义详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机字段管理"
                ],
                "summary": "获取单个主机字段",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "字段ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "主机字段信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/arboris_src_models.HostField"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "字段不存在",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "更新主机字段定义信息（字段类型不可修改）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机字段管理"
                ],
                "summary": "更新主机字段",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "字段ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "主机字段更新参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/arboris_src_models.HostField"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "字段不存在",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "删除主机字段定义",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机字段管理"
                ],
                "summary": "删除主机字段",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "字段ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "字段不存在",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/ams-ce/hosts/fields": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取所有主机自定义字段定义列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机字段管理"
                ],
                "summary": "获取主机字段列表",
                "responses": {
                    "200": {
                        "description": "主机字段列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/arboris_src_models.HostField"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "创建新的主机自定义字段定义",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机字段管理"
                ],
                "summary": "创建主机字段",
                "parameters": [
                    {
                        "description": "主机字段参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/arboris_src_models.HostField"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/ams-ce/hosts/fields/values": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "获取多个主机的指定自定义字段值，支持分页",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机字段管理"
                ],
                "summary": "批量获取主机字段值",
                "parameters": [
                    {
                        "description": "查询参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.hostFieldValueForm"
                        }
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "p",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "主机字段值列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/src_modules_ams_http.HostFieldValueListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/ams-ce/hosts/node": {
            "put": {
                "security": [
                    {
                        "TokenAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "将主机设备挂载到多个节点，支持一台主机同时挂载到多个节点",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "主机挂载到节点",
                "parameters": [
                    {
                        "description": "主机节点挂载参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.hostNodeForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "挂载成功",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/ams-ce/hosts/note": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "批量修改主机设备的备注信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "修改主机备注",
                "parameters": [
                    {
                        "description": "主机备注修改参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.hostNoteForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改成功",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/ams-ce/hosts/tenant": {
            "put": {
                "security": [
                    {
                        "TokenAuth": []
                    },
                    {
                        "CookieAuth": []
                    }
                ],
                "description": "管理员修改主机设备的租户，相当于分配设备到指定租户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "修改主机租户",
                "parameters": [
                    {
                        "description": "主机租户修改参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.hostTenantForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改成功",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/src_modules_ams_http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/api/ams-ce/ping": {
            "get": {
                "description": "检查 AMS 服务是否正常运行",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "text/plain"
                ],
                "tags": [
                    "系统管理"
                ],
                "summary": "健康检查",
                "responses": {
                    "200": {
                        "description": "pong",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "arboris_src_models.Host": {
            "type": "object",
            "properties": {
                "cate": {
                    "type": "string"
                },
                "clock": {
                    "type": "integer"
                },
                "cpu": {
                    "type": "string"
                },
                "cpu_model": {
                    "type": "string"
                },
                "disk": {
                    "type": "string"
                },
                "gpu": {
                    "type": "string"
                },
                "gpu_model": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "idc": {
                    "type": "string"
                },
                "ident": {
                    "type": "string"
                },
                "ip": {
                    "type": "string"
                },
                "kernel_version": {
                    "type": "string"
                },
                "manufacturer": {
                    "type": "string"
                },
                "mem": {
                    "type": "string"
                },
                "model": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "note": {
                    "type": "string"
                },
                "os_version": {
                    "type": "string"
                },
                "rack": {
                    "type": "string"
                },
                "sn": {
                    "type": "string"
                },
                "tenant": {
                    "type": "string"
                },
                "zone": {
                    "type": "string"
                }
            }
        },
        "arboris_src_models.HostField": {
            "type": "object",
            "properties": {
                "field_cate": {
                    "type": "string"
                },
                "field_extra": {
                    "type": "string"
                },
                "field_ident": {
                    "type": "string"
                },
                "field_name": {
                    "type": "string"
                },
                "field_required": {
                    "type": "integer"
                },
                "field_type": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                }
            }
        },
        "arboris_src_models.HostFieldValue": {
            "type": "object",
            "properties": {
                "field_ident": {
                    "type": "string"
                },
                "field_value": {
                    "type": "string"
                },
                "host_id": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                }
            }
        },
        "src_modules_ams_http.ApiResponse": {
            "type": "object",
            "properties": {
                "dat": {},
                "err": {
                    "type": "string"
                }
            }
        },
        "src_modules_ams_http.HostFieldValueListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {}
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "src_modules_ams_http.HostFilterItem": {
            "type": "object",
            "properties": {
                "cate": {
                    "type": "string"
                },
                "cpu": {
                    "type": "string"
                },
                "cpu_model": {
                    "type": "string"
                },
                "disk": {
                    "type": "string"
                },
                "field_values": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "gpu": {
                    "type": "string"
                },
                "gpu_model": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "idc": {
                    "type": "string"
                },
                "ident": {
                    "type": "string"
                },
                "ip": {
                    "type": "string"
                },
                "kernel_version": {
                    "type": "string"
                },
                "mem": {
                    "type": "string"
                },
                "model": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "node_paths": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "note": {
                    "type": "string"
                },
                "os_version": {
                    "type": "string"
                },
                "rack": {
                    "type": "string"
                },
                "sn": {
                    "type": "string"
                },
                "tenant": {
                    "type": "string"
                },
                "zone": {
                    "type": "string"
                }
            }
        },
        "src_modules_ams_http.HostFilterResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/src_modules_ams_http.HostFilterItem"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "src_modules_ams_http.HostListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/arboris_src_models.Host"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "src_modules_ams_http.ImportResult": {
            "type": "object",
            "properties": {
                "failed_count": {
                    "type": "integer"
                },
                "import_id": {
                    "type": "string"
                },
                "success_count": {
                    "type": "integer"
                },
                "total_rows": {
                    "type": "integer"
                }
            }
        },
        "src_modules_ams_http.hostAddForm": {
            "type": "object",
            "required": [
                "ident",
                "ip"
            ],
            "properties": {
                "cate": {
                    "type": "string"
                },
                "cpu": {
                    "type": "string"
                },
                "cpu_model": {
                    "type": "string"
                },
                "disk": {
                    "type": "string"
                },
                "fields": {
                    "description": "自定义字段",
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "gpu": {
                    "type": "string"
                },
                "gpu_model": {
                    "type": "string"
                },
                "idc": {
                    "type": "string"
                },
                "ident": {
                    "type": "string"
                },
                "ip": {
                    "type": "string"
                },
                "kernel_version": {
                    "type": "string"
                },
                "manufacturer": {
                    "type": "string"
                },
                "mem": {
                    "type": "string"
                },
                "model": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "note": {
                    "type": "string"
                },
                "os_version": {
                    "type": "string"
                },
                "rack": {
                    "type": "string"
                },
                "sn": {
                    "type": "string"
                },
                "tenant": {
                    "type": "string"
                },
                "zone": {
                    "type": "string"
                }
            }
        },
        "src_modules_ams_http.hostCateForm": {
            "type": "object",
            "properties": {
                "cate": {
                    "type": "string"
                },
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "src_modules_ams_http.hostFieldValueForm": {
            "type": "object",
            "required": [
                "field_idents",
                "host_ids"
            ],
            "properties": {
                "field_idents": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "host_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "src_modules_ams_http.hostFilterForm": {
            "type": "object",
            "properties": {
                "cate": {
                    "description": "类别精确匹配，支持多选",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "cpu_model": {
                    "description": "CPU型号精确匹配，支持多选",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "fields": {
                    "description": "自定义字段过滤",
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "field_ident": {
                                "description": "字段标识符",
                                "type": "string"
                            },
                            "is_like": {
                                "description": "是否使用模糊匹配",
                                "type": "boolean"
                            },
                            "values": {
                                "description": "字段值，支持多选",
                                "type": "array",
                                "items": {
                                    "type": "string"
                                }
                            }
                        }
                    }
                },
                "gpu_model": {
                    "description": "GPU型号精确匹配，支持多选",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "idc": {
                    "description": "IDC精确匹配，支持多选",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "ident": {
                    "description": "标识符模糊匹配",
                    "type": "string"
                },
                "ip": {
                    "description": "基础字段过滤",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "kernel_version": {
                    "description": "内核版本模糊匹配",
                    "type": "string"
                },
                "model": {
                    "description": "型号精确匹配，支持多选",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "name": {
                    "description": "名称模糊匹配",
                    "type": "string"
                },
                "note": {
                    "description": "备注模糊匹配",
                    "type": "string"
                },
                "os_version": {
                    "description": "操作系统版本模糊匹配",
                    "type": "string"
                },
                "rack": {
                    "description": "机架精确匹配，支持多选",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "sn": {
                    "description": "SN模糊匹配",
                    "type": "string"
                },
                "tenant": {
                    "description": "租户精确匹配，支持多选",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "zone": {
                    "description": "可用区精确匹配，支持多选",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "src_modules_ams_http.hostNodeForm": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "nodeids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "src_modules_ams_http.hostNoteForm": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "note": {
                    "type": "string"
                }
            }
        },
        "src_modules_ams_http.hostTenantForm": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "tenant": {
                    "type": "string"
                }
            }
        },
        "src_modules_ams_http.idsOrIpsForm": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "ips": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "description": "API Token 认证",
            "type": "apiKey",
            "name": "X-User-Token",
            "in": "header"
        },
        "CookieAuth": {
            "description": "Cookie 认证 (ecmc-sid)",
            "type": "apiKey",
            "name": "Cookie",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8002",
	BasePath:         "/",
	Schemes:          []string{},
	Title:            "AMS API",
	Description:      "Arboris AMS 模块 API 文档",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
