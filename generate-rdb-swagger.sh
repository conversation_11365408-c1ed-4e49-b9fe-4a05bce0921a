#!/bin/bash

# 生成RDB模块的Swagger文档
# 确保不包含AMS模块的内容

echo "=== 生成RDB模块Swagger文档 ==="

# 检查swag工具是否存在
if ! command -v ~/go/bin/swag &> /dev/null; then
    echo "swag工具未找到，正在安装..."
    go install github.com/swaggo/swag/cmd/swag@latest
    if [ $? -ne 0 ]; then
        echo "安装swag工具失败"
        exit 1
    fi
fi

# 进入RDB模块目录
cd src/modules/rdb

# 生成swagger文档
echo "正在生成swagger文档..."
~/go/bin/swag init \
    --generalInfo rdb.go \
    --dir . \
    --output ../../../docs-rdb \
    --parseDependency \
    --parseInternal

if [ $? -eq 0 ]; then
    echo "✅ Swagger文档生成成功！"
    echo "📁 文档位置: docs-rdb/"
    echo "📄 文件列表:"
    ls -la ../../../docs-rdb/
    
    echo ""
    echo "🔍 验证新增的用户接口:"
    grep -E "(enable|disable)" ../../../docs-rdb/swagger.yaml | head -4
    
    echo ""
    echo "📊 API统计:"
    echo "总接口数: $(grep -c 'paths:' ../../../docs-rdb/swagger.yaml)"
    echo "用户相关接口: $(grep -c '/api/rdb/user' ../../../docs-rdb/swagger.yaml)"
    
else
    echo "❌ Swagger文档生成失败"
    exit 1
fi

# 返回原目录
cd ../../..

echo ""
echo "🚀 可以通过以下方式查看文档:"
echo "1. 在线查看: http://localhost:8000/swagger/index.html (如果RDB服务已启动)"
echo "2. 本地文件: docs-rdb/swagger.yaml 或 docs-rdb/swagger.json"
