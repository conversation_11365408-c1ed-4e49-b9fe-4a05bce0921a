#!/bin/bash

# 测试所有事件发布功能

BASE_URL="http://localhost:8000"
TOKEN="41a7ae8949f77c3deb3a351bd6927734"

echo "=== 测试所有事件发布功能 ==="

# 1. 测试用户事件
echo "1. 测试用户事件..."
echo "1.1 创建用户..."
curl -s -X 'POST' \
  "${BASE_URL}/api/rdb/users" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}" \
  -H 'Content-Type: application/json' \
  -d '{
  "dispname": "事件测试用户",
  "email": "<EMAIL>",
  "im": "18177777777",
  "is_root": 0,
  "leader_id": 0,
  "password": "EventTest123!",
  "status": 0,
  "typ": 0,
  "username": "eventtest"
}' > /dev/null

echo "1.2 等待用户创建事件处理..."
sleep 3

# 2. 测试团队事件
echo "2. 测试团队事件..."
echo "2.1 创建团队..."
curl -s -X 'POST' \
  "${BASE_URL}/api/rdb/teams" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}" \
  -H 'Content-Type: application/json' \
  -d '{
  "ident": "event-test-team",
  "name": "事件测试团队",
  "note": "用于测试所有事件的团队",
  "mgmt": 1
}' > /dev/null

echo "2.2 等待团队创建事件处理..."
sleep 3

# 3. 测试节点事件（如果有节点API）
echo "3. 测试节点事件..."
echo "3.1 创建节点..."
curl -s -X 'POST' \
  "${BASE_URL}/api/rdb/tree" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}" \
  -H 'Content-Type: application/json' \
  -d '{
  "pid": 0,
  "ident": "event-test-node",
  "name": "事件测试节点",
  "note": "用于测试节点事件的节点",
  "cate": "host",
  "leaf": 1
}' > /dev/null 2>&1 || echo "节点创建API可能不存在或需要不同的参数"

echo "3.2 等待节点创建事件处理..."
sleep 3

# 4. 获取创建的资源ID
echo "4. 获取创建的资源信息..."
USER_LIST=$(curl -s -X 'GET' \
  "${BASE_URL}/api/rdb/users?limit=10&p=1" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}")

TEAM_LIST=$(curl -s -X 'GET' \
  "${BASE_URL}/api/rdb/teams?limit=10&p=1" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}")

# 简单提取ID
USER_ID=$(echo "$USER_LIST" | grep -o '"id":[0-9]*' | tail -1 | cut -d':' -f2)
TEAM_ID=$(echo "$TEAM_LIST" | grep -o '"id":[0-9]*' | tail -1 | cut -d':' -f2)

echo "用户ID: $USER_ID"
echo "团队ID: $TEAM_ID"

# 5. 测试团队成员事件
if [ -n "$USER_ID" ] && [ -n "$TEAM_ID" ]; then
    echo "5. 测试团队成员事件..."
    echo "5.1 添加用户到团队..."
    curl -s -X 'PUT' \
      "${BASE_URL}/api/rdb/team/${TEAM_ID}/users/bind" \
      -H 'accept: application/json' \
      -H "X-User-Token: ${TOKEN}" \
      -H 'Content-Type: application/json' \
      -d "{
      \"admin_ids\": [],
      \"member_ids\": [${USER_ID}]
    }" > /dev/null

    echo "5.2 等待团队添加成员事件处理..."
    sleep 3

    echo "5.3 从团队移除用户..."
    curl -s -X 'PUT' \
      "${BASE_URL}/api/rdb/team/${TEAM_ID}/users/unbind" \
      -H 'accept: application/json' \
      -H "X-User-Token: ${TOKEN}" \
      -H 'Content-Type: application/json' \
      -d "{
      \"user_ids\": [${USER_ID}]
    }" > /dev/null

    echo "5.4 等待团队移除成员事件处理..."
    sleep 3
fi

# 6. 测试用户状态变更事件
if [ -n "$USER_ID" ]; then
    echo "6. 测试用户状态变更事件..."
    echo "6.1 禁用用户..."
    curl -s -X 'PUT' \
      "${BASE_URL}/api/rdb/user/${USER_ID}/disable" \
      -H 'accept: application/json' \
      -H "X-User-Token: ${TOKEN}" > /dev/null

    echo "6.2 等待用户禁用事件处理..."
    sleep 3

    echo "6.3 启用用户..."
    curl -s -X 'PUT' \
      "${BASE_URL}/api/rdb/user/${USER_ID}/enable" \
      -H 'accept: application/json' \
      -H "X-User-Token: ${TOKEN}" > /dev/null

    echo "6.4 等待用户启用事件处理..."
    sleep 3
fi

# 7. 清理测试数据
echo "7. 清理测试数据..."
if [ -n "$TEAM_ID" ]; then
    curl -s -X 'DELETE' "${BASE_URL}/api/rdb/team/${TEAM_ID}" -H "X-User-Token: ${TOKEN}" > /dev/null
fi
if [ -n "$USER_ID" ]; then
    curl -s -X 'DELETE' "${BASE_URL}/api/rdb/user/${USER_ID}" -H "X-User-Token: ${TOKEN}" > /dev/null
fi

echo "8. 等待清理事件处理..."
sleep 3

echo -e "\n=== 所有事件测试完成 ==="
echo "请检查jumpserver-sync日志确认以下事件是否正确处理："
echo "- user.create (用户创建)"
echo "- team.create (团队创建)"
echo "- node.create (节点创建，如果支持)"
echo "- team.add_member (团队添加成员)"
echo "- team.remove_member (团队移除成员)"
echo "- user.disable (用户禁用)"
echo "- user.enable (用户启用)"
echo "- team.delete (团队删除)"
echo "- user.delete (用户删除)"
echo ""
echo "所有事件现在都应该有真正的业务逻辑实现，不再是mock数据！"
