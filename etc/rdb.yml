logger:
  dir: logs/rdb
  level: INFO
  keepHours: 24

http:
  mode: release
  session:
    cookieName: ecmc-sid
    domain: ""
    httpOnly: true
    gcInterval: 60
    cookieLifetime: 86400 # 单位秒，0: 与浏览器相同

i18n:
  lang: zh

auth:
  extraMode:
    enable: false        # enable whiteList, login retry lock, userControl, ...
    whiteList: false
    frozenDays: 90      # frozen time (day)
    writenOffDays: 365  # writenOff time (day)

# Redis配置 - 用于事件发布
redis:
  addr: "10.1.4.213:6379"
  password: "4KyCLw2TEMWsWCz8"
  db: 0

# 事件配置 - JumpServer同步
events:
  enable: true
  stream_name: "arboris:sync:events"