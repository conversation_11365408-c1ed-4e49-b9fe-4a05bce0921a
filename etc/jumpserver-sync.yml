# JumpServer同步服务配置文件示例

# 日志配置（与RDB模块保持一致）
logger:
  dir: "logs/jumpserver-sync"
  level: "INFO"
  keepHours: 24  # 24小时

# 服务器配置
http:
  mode: release

# Redis配置
redis:
  addr: "**********:6379"
  password: "4KyCLw2TEMWsWCz8"
  db: 0
  pool_size: 10
  min_idle_conns: 5

# JumpServer配置
jumpserver:
  base_url: "http://**********:8090"
  username: "admin"
  password: "root.2025"
  token: "e7261a6a44d0a3991dbd77c9d308be3e887340b6"  # 使用token认证
  organization: "00000000-0000-0000-0000-000000000002"
  timeout: 30s
  retry:
    max_attempts: 3
    initial_interval: 1s
    max_interval: 30s
    multiplier: 2.0

# 同步配置
sync:
  # 消费者配置
  consumer:
    stream: "arboris:sync:events"
    group: "jumpserver-sync"
    consumer: "jumpserver-sync-1"
    batch_size: 10
    poll_interval: 1s

  # 映射规则配置
  mapping:
    # 节点路径映射规则
    # RDB的node path格式: inner.code.master
    # 直接使用相同的路径，无需转换
    node_rules: []
    # 示例：如果需要路径前缀转换（通常不需要）
    # - pattern: "^inner\\."
    #   target_prefix: "internal."
    #   description: "内部节点前缀转换"

    # 默认情况下，RDB路径直接映射到JumpServer
    # inner.code.master -> inner.code.master

    # 机器映射规则
    machine_rules:
      - condition: "os_version~Windows"
        platform: "Windows"
        protocols:
          - name: "rdp"
            port: 3389
      - condition: "cate=server"
        platform: "Linux"
        protocols:
          - name: "ssh"
            port: 22

    # 权限映射规则（暂时注释掉，后续可以扩展）
    # permission_rules:
    #   - role_name: "管理员"
    #     actions: ["connect", "upload", "download"]
    #   - role_name: "只读用户"
    #     actions: ["connect"]

  # 同步规则配置
  rules:
    # 包含的节点路径（只同步这些路径下的节点）
    include_paths:
#      - "/公司"
#      - "/部门"

    # 排除的节点路径（不同步这些路径下的节点）
    exclude_paths:
#      - "/测试"
#      - "/临时"

    # 过滤器
    filters:
      status: "active"
      sync_enabled: "true"
