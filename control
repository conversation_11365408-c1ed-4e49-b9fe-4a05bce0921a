#!/bin/bash

# release version
version=0.0.1

CWD=$(cd $(dirname $0)/; pwd)
cd $CWD

usage()
{
	echo $"Usage: $0 {start|stop|restart|status|build|build_local|pack|buildimage} <module>"
	exit 0
}

start_all()
{
	test -x arboris-ams && start ams
	test -x arboris-rdb && start rdb
	test -x arboris-jump-sync && start jumpserver-sync
}

start()
{
	mod=$1
	if [ "x${mod}" = "x" ]; then
		usage
		return
	fi

	if [ "x${mod}" = "xall" ]; then
		start_all
		return
	fi

	binfile=arboris-${mod}

	if [ ! -f $binfile ]; then
		echo "file[$binfile] not found"
		exit 1
	fi

	if [ $(ps aux|grep -v grep|grep -v control|grep "$binfile" -c) -gt 0 ]; then
		echo "${mod} already started"
		return
	fi

	mkdir -p logs/$mod
	nohup $CWD/$binfile &> logs/${mod}/stdout.log &

	for((i=1;i<=15;i++)); do
		if [ $(ps aux|grep -v grep|grep -v control|grep "$binfile" -c) -gt 0 ]; then
			echo "${mod} started"
			return
		fi
		sleep 0.2
	done

	echo "cannot start ${mod}"
	exit 1
}

stop_all()
{
	test -x arboris-ams && stop ams
	test -x arboris-rdb && stop rdb
	test -x arboris-jump-sync && stop jumpserver-sync
}

stop()
{
	mod=$1
	if [ "x${mod}" = "x" ]; then
		usage
		return
	fi

	if [ "x${mod}" = "xall" ]; then
		stop_all
		return
	fi

	binfile=arboris-${mod}

	if [ $(ps aux|grep -v grep|grep -v control|grep "$binfile" -c) -eq 0 ]; then
		echo "${mod} already stopped"
		return
	fi

	ps aux|grep -v grep|grep -v control|grep "$binfile"|awk '{print $2}'|xargs kill
	for((i=1;i<=15;i++)); do
		if [ $(ps aux|grep -v grep|grep -v control|grep "$binfile" -c) -eq 0 ]; then
			echo "${mod} stopped"
			return
		fi
		sleep 0.2
	done

	echo "cannot stop $mod"
	exit 1
}

restart()
{
	mod=$1
	if [ "x${mod}" = "x" ]; then
		usage
		return
	fi

	if [ "x${mod}" = "xall" ]; then
		stop_all
		start_all
		return
	fi

	stop $mod
	start $mod

	status
}

status()
{
	ps aux|grep -v grep|grep "arboris-"
}

build_one()
{
	mod=$1
	echo -n "building ${mod} ... "
	go build -ldflags "-X main.version=${version}" -o arboris-${mod} src/modules/${mod}/${mod}.go
	echo "done"
}

build_local_one()
{
	mod=$1
	echo -n "building ${mod} ... "
	go build -mod=vendor -ldflags "-X main.version=${version}" -o arboris-${mod} src/modules/${mod}/${mod}.go
	echo "done"
}

build()
{
	export GO111MODULE=on

	mod=$1
	if [ "x${mod}" = "x" ]; then
		build_one ams
		build_one rdb
		build_one jumpserver-sync
		return
	fi

	build_one $mod
}

build_local()
{
	export GO111MODULE=on

	mod=$1
	if [ "x${mod}" = "x" ]; then
		build_local_one ams
		build_local_one rdb
		build_local_one jumpserver-sync
		return
	fi

	build_local_one $mod
}

reload()
{
	mod=$1
	if [ "x${mod}" = "x" ]; then
		echo "arg: <mod> is necessary"
		return
	fi

	build_one $mod
	restart $mod
}

pack()
{
	clock=$(date +%s)
	mkdir -p ~/arboris.bak.$clock

	if ls etc/*.local.yml &>/dev/null; then
	  mv etc/*.local.yml ~/arboris.bak.$clock
	fi

	tar zcvf arboris-${version}.tar.gz control sql etc arboris-*

	if ls ~/arboris.bak.$clock/*.local.yml &>/dev/null; then
	  mv  ~/arboris.bak.$clock/*.local.yml etc/
	fi

	rm -rf ~/arboris.bak.$clock
}

buildimage()
{
	image_name=$1
	if [ "x${image_name}" = "x" ]; then
		echo "Usage: $0 buildimage <image_name>"
		exit 1
	fi

  if [ ! -f "arboris-${version}.tar.gz" ]; then
          echo "Error: arboris-${version}.tar.gz not found"
          exit 1
  fi

  cp arboris-${version}.tar.gz dockerfiles/arboris/
  cd dockerfiles/arboris/

	# 构建镜像
	echo "Building docker image: ${image_name}"
	if ! docker build -t ${image_name} .; then
		echo "Error: Docker build failed"
		rm -f arboris-${version}.tar.gz
		cd -
		exit 1
	fi

	rm -f arboris-${version}.tar.gz
	cd -
	echo "Successfully built image: ${image_name}"
}

exec()
{
	params=${@:2}

	if [ ${#2} -gt 0 ]; then
		for param in $params
		do
			$1 $param
			if [ "x${mod}" = "xall" ]; then
				break
			fi
		done
	else
		echo $1
		$1
	fi
}

case "$1" in
	start)
		exec start ${@:2}
		;;
	stop)
		exec stop ${@:2}
		;;
	restart)
		exec restart ${@:2}
		;;
	status)
		status
		;;
	build)
		exec build ${@:2}
		;;
	build_local)
		exec build_local ${@:2}
		;;
	reload)
		exec reload ${@:2}
		;;
	pack)
		exec pack ${@:2}
		;;
	buildimage)
		exec buildimage ${@:2}
		;;
	*)
		usage
esac
