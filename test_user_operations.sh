#!/bin/bash

# 测试用户操作的脚本
# 包括创建、启用、禁用、删除用户

BASE_URL="http://localhost:8000"
TOKEN="41a7ae8949f77c3deb3a351bd6927734"

echo "=== 测试用户操作 ==="

# 1. 创建用户
echo "1. 创建用户 testuser2..."
curl -X 'POST' \
  "${BASE_URL}/api/rdb/users" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}" \
  -H 'Content-Type: application/json' \
  -d '{
  "dispname": "测试用户2",
  "email": "<EMAIL>",
  "im": "18133333333",
  "is_root": 0,
  "leader_id": 0,
  "password": "TestPassword123!",
  "status": 0,
  "typ": 0,
  "username": "testuser2"
}'

echo -e "\n等待3秒让事件处理完成..."
sleep 3

# 2. 获取用户ID（假设用户ID为最新创建的）
echo -e "\n2. 获取用户列表..."
USER_LIST=$(curl -s -X 'GET' \
  "${BASE_URL}/api/rdb/users?limit=10&p=1" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}")

echo "用户列表: $USER_LIST"

# 提取testuser2的ID（这里简化处理，实际应该解析JSON）
USER_ID=$(echo "$USER_LIST" | grep -o '"id":[0-9]*' | tail -1 | cut -d':' -f2)
echo "用户ID: $USER_ID"

if [ -z "$USER_ID" ]; then
    echo "无法获取用户ID，退出测试"
    exit 1
fi

# 3. 禁用用户
echo -e "\n3. 禁用用户..."
curl -X 'PUT' \
  "${BASE_URL}/api/rdb/user/${USER_ID}/disable" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}"

echo -e "\n等待3秒让事件处理完成..."
sleep 3

# 4. 启用用户
echo -e "\n4. 启用用户..."
curl -X 'PUT' \
  "${BASE_URL}/api/rdb/user/${USER_ID}/enable" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}"

echo -e "\n等待3秒让事件处理完成..."
sleep 3

# 5. 删除用户
echo -e "\n5. 删除用户..."
curl -X 'DELETE' \
  "${BASE_URL}/api/rdb/user/${USER_ID}" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}"

echo -e "\n等待3秒让事件处理完成..."
sleep 3

echo -e "\n=== 测试完成 ==="
echo "请检查JumpServer中的用户状态变化"
