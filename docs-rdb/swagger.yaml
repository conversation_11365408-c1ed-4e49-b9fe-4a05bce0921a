basePath: /
definitions:
  config.Op:
    properties:
      cn:
        type: string
      en:
        type: string
    type: object
  config.OpsGroup:
    properties:
      ops:
        items:
          $ref: '#/definitions/config.Op'
        type: array
      title:
        type: string
    type: object
  config.OpsSystem:
    properties:
      groups:
        items:
          $ref: '#/definitions/config.OpsGroup'
        type: array
      system:
        type: string
    type: object
  http.ApiResponse:
    properties:
      dat: {}
      err:
        type: string
    type: object
  http.LoginLogListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/models.LoginLog'
        type: array
      total:
        type: integer
    type: object
  http.OperationLogListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/models.OperationLog'
        type: array
      total:
        type: integer
    type: object
  http.ResourceCountByCate:
    properties:
      count:
        type: integer
      name:
        type: string
    type: object
  http.TeamDetailResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/models.User'
        type: array
      team:
        $ref: '#/definitions/models.Team'
      total:
        type: integer
    type: object
  http.TeamListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/models.Team'
        type: array
      total:
        type: integer
    type: object
  http.UserListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/models.User'
        type: array
      total:
        type: integer
    type: object
  http.WhiteListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/models.WhiteList'
        type: array
      total:
        type: integer
    type: object
  http.containerSyncForm:
    properties:
      items:
        items:
          $ref: '#/definitions/http.v1ContainersRegisterItem'
        type: array
      name:
        type: string
      type:
        type: string
    required:
    - name
    - type
    type: object
  http.createWhiteListInput:
    properties:
      endIp:
        type: string
      endTime:
        type: integer
      startIp:
        type: string
      startTime:
        type: integer
    type: object
  http.idsForm:
    properties:
      ids:
        items:
          type: integer
        type: array
    type: object
  http.loginInput:
    properties:
      args:
        items:
          type: string
        type: array
      password:
        type: string
      remote_addr:
        type: string
      type:
        type: string
      username:
        type: string
    type: object
  http.nodeCatePostForm:
    properties:
      icon_color:
        type: string
      ident:
        type: string
      name:
        type: string
    type: object
  http.nodeCatePutForm:
    properties:
      icon_color:
        type: string
      name:
        type: string
    type: object
  http.nodeForm:
    properties:
      admin_ids:
        items:
          type: integer
        type: array
      cate:
        type: string
      ident:
        type: string
      leaf:
        type: integer
      name:
        type: string
      note:
        type: string
      pid:
        type: integer
      proxy:
        type: integer
    type: object
  http.resourceBindForm:
    properties:
      field:
        type: string
      items:
        items:
          type: string
        type: array
    type: object
  http.resourceNotePutForm:
    properties:
      ids:
        items:
          type: integer
        type: array
      note:
        type: string
    required:
    - ids
    type: object
  http.roleForm:
    properties:
      cate:
        type: string
      name:
        type: string
      note:
        type: string
      operations:
        items:
          type: string
        type: array
    type: object
  http.roleUnderNodeDelForm:
    properties:
      role_id:
        type: integer
      username:
        type: string
    type: object
  http.roleUnderNodeForm:
    properties:
      role_id:
        type: integer
      usernames:
        items:
          type: string
        type: array
    type: object
  http.selfPasswordForm:
    properties:
      newpass:
        type: string
      oldpass:
        type: string
      username:
        type: string
    required:
    - newpass
    - oldpass
    - username
    type: object
  http.selfProfileForm:
    properties:
      dispname:
        type: string
      email:
        type: string
      im:
        type: string
      intro:
        type: string
      phone:
        type: string
      portrait:
        type: string
    type: object
  http.selfTokenForm:
    properties:
      token:
        type: string
    type: object
  http.teamForm:
    properties:
      ident:
        type: string
      mgmt:
        type: integer
      name:
        type: string
      note:
        type: string
    type: object
  http.teamUserBindForm:
    properties:
      admin_ids:
        items:
          type: integer
        type: array
      member_ids:
        items:
          type: integer
        type: array
    type: object
  http.teamUserUnbindForm:
    properties:
      user_ids:
        items:
          type: integer
        type: array
    type: object
  http.updateWhiteListInput:
    properties:
      endIp:
        type: string
      endTime:
        type: integer
      startIp:
        type: string
      startTime:
        type: integer
    type: object
  http.userInviteForm:
    properties:
      dispname:
        type: string
      email:
        type: string
      im:
        type: string
      password:
        type: string
      phone:
        type: string
      token:
        type: string
      username:
        type: string
    required:
    - password
    - token
    - username
    type: object
  http.userPasswordForm:
    properties:
      password:
        type: string
    required:
    - password
    type: object
  http.userProfileForm:
    properties:
      dispname:
        type: string
      email:
        type: string
      im:
        type: string
      is_root:
        type: integer
      leader_id:
        type: integer
      organization:
        type: string
      password:
        type: string
      phone:
        type: string
      status:
        type: integer
      typ:
        type: integer
      username:
        type: string
    type: object
  http.v1ContainersRegisterItem:
    properties:
      cate:
        type: string
      extend:
        type: string
      ident:
        type: string
      labels:
        type: string
      name:
        type: string
      nid:
        type: integer
      uuid:
        type: string
    type: object
  models.AuthConfig:
    properties:
      lockTime:
        type: integer
      maxConnIdleTime:
        type: integer
      maxNumErr:
        type: integer
      maxSessionNumber:
        type: integer
      pwdExpiresIn:
        type: integer
      pwdHistorySize:
        type: integer
      pwdMinLenght:
        type: integer
      pwdMustInclude:
        items:
          type: string
        type: array
      pwdMustIncludeFlag:
        type: integer
    type: object
  models.LoginLog:
    properties:
      client:
        type: string
      clock:
        type: integer
      err:
        type: string
      id:
        type: integer
      loginout:
        type: string
      username:
        type: string
    type: object
  models.Node:
    properties:
      admins:
        items:
          $ref: '#/definitions/models.User'
        type: array
      cate:
        type: string
      creator:
        type: string
      icon_char:
        type: string
      icon_color:
        type: string
      id:
        type: integer
      ident:
        type: string
      last_updated:
        type: string
      leaf:
        type: integer
      name:
        type: string
      note:
        type: string
      path:
        type: string
      pid:
        type: integer
      proxy:
        type: integer
    type: object
  models.NodeCate:
    properties:
      icon_color:
        type: string
      id:
        type: integer
      ident:
        type: string
      name:
        type: string
      protected:
        type: integer
    type: object
  models.NodeCateField:
    properties:
      cate:
        type: string
      field_extra:
        type: string
      field_ident:
        type: string
      field_name:
        type: string
      field_required:
        type: integer
      field_type:
        type: string
      id:
        type: integer
      last_updated:
        type: string
    type: object
  models.NodeFieldValue:
    properties:
      field_ident:
        type: string
      field_value:
        type: string
      id:
        type: integer
      node_id:
        type: integer
    type: object
  models.OperationLog:
    properties:
      clock:
        type: integer
      detail:
        type: string
      id:
        type: integer
      res_cl:
        type: string
      res_id:
        type: string
      username:
        type: string
    type: object
  models.Resource:
    properties:
      cate:
        type: string
      extend:
        type: string
      id:
        type: integer
      ident:
        type: string
      labels:
        type: string
      last_updated:
        type: string
      name:
        type: string
      note:
        type: string
      source_id:
        type: integer
      source_type:
        type: string
      tenant:
        type: string
      uuid:
        type: string
    type: object
  models.ResourceBinding:
    properties:
      id:
        type: integer
      ident:
        type: string
      name:
        type: string
      nodes:
        items:
          $ref: '#/definitions/models.Node'
        type: array
      uuid:
        type: string
    type: object
  models.ResourceRegisterItem:
    properties:
      cate:
        type: string
      extend:
        type: string
      ident:
        type: string
      labels:
        type: string
      name:
        type: string
      nid:
        type: integer
      source_id:
        type: integer
      source_type:
        type: string
      uuid:
        type: string
    type: object
  models.Role:
    properties:
      cate:
        type: string
      id:
        type: integer
      name:
        type: string
      note:
        type: string
    type: object
  models.RoleGlobalUser:
    properties:
      role_id:
        type: integer
      user_id:
        type: integer
    type: object
  models.Team:
    properties:
      creator:
        type: integer
      id:
        type: integer
      ident:
        type: string
      last_updated:
        type: string
      mgmt:
        type: integer
      name:
        type: string
      note:
        type: string
    type: object
  models.User:
    properties:
      active_begin:
        type: integer
      active_end:
        type: integer
      create_at:
        type: string
      dispname:
        type: string
      email:
        type: string
      id:
        type: integer
      im:
        type: string
      intro:
        type: string
      is_root:
        type: integer
      leader_id:
        type: integer
      leader_name:
        type: string
      locked_at:
        type: integer
      logged_at:
        type: integer
      login_err_num:
        type: integer
      organization:
        type: string
      phone:
        type: string
      portrait:
        type: string
      pwd_expires_at:
        type: integer
      pwd_updated_at:
        type: integer
      status:
        type: integer
      type:
        type: integer
      updated_at:
        type: integer
      username:
        type: string
      uuid:
        type: string
    type: object
  models.UserToken:
    properties:
      token:
        type: string
      user_id:
        type: integer
      username:
        type: string
    type: object
  models.WhiteList:
    properties:
      createdAt:
        type: integer
      creator:
        type: string
      endIp:
        type: string
      endTime:
        type: integer
      id:
        type: integer
      startIp:
        type: string
      startTime:
        type: integer
      updateAt:
        type: integer
      updater:
        type: string
    type: object
host: localhost:8000
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: Arboris RDB 模块 API 文档
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Arboris RDB API
  version: "1.0"
paths:
  /api/rdb/auth/login:
    post:
      consumes:
      - application/json
      description: 用户通过用户名和密码登录系统
      parameters:
      - description: 登录信息
        in: body
        name: login
        required: true
        schema:
          $ref: '#/definitions/http.loginInput'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 认证失败
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      summary: 用户登录
      tags:
      - 认证管理
  /api/rdb/auth/logout:
    get:
      consumes:
      - application/json
      description: 用户登出系统，清除会话信息
      produces:
      - application/json
      responses:
        "200":
          description: 登出成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - CookieAuth: []
      summary: 用户登出
      tags:
      - 认证管理
  /api/rdb/auth/white-list:
    get:
      consumes:
      - application/json
      description: 获取IP白名单列表，支持分页和搜索
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      - description: 搜索关键字
        in: query
        name: query
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 白名单列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.WhiteListResponse'
              type: object
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取白名单列表
      tags:
      - 白名单管理
    post:
      consumes:
      - application/json
      description: 创建新的IP白名单条目，用于访问控制
      parameters:
      - description: 白名单信息
        in: body
        name: whitelist
        required: true
        schema:
          $ref: '#/definitions/http.createWhiteListInput'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功，返回ID
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  additionalProperties: true
                  type: object
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 创建白名单条目
      tags:
      - 白名单管理
  /api/rdb/auth/white-list/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定ID的白名单条目
      parameters:
      - description: 白名单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 白名单不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 删除白名单条目
      tags:
      - 白名单管理
    get:
      consumes:
      - application/json
      description: 根据ID获取指定的白名单条目详情
      parameters:
      - description: 白名单ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 白名单详情
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.WhiteList'
              type: object
        "404":
          description: 白名单不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取单个白名单条目
      tags:
      - 白名单管理
    put:
      consumes:
      - application/json
      description: 更新指定ID的白名单条目信息
      parameters:
      - description: 白名单ID
        in: path
        name: id
        required: true
        type: integer
      - description: 更新的白名单信息
        in: body
        name: whitelist
        required: true
        schema:
          $ref: '#/definitions/http.updateWhiteListInput'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 白名单不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 更新白名单条目
      tags:
      - 白名单管理
  /api/rdb/can-do-global-op:
    get:
      consumes:
      - application/json
      description: 检查指定用户是否有权限执行全局操作
      parameters:
      - description: 用户名
        in: query
        name: username
        required: true
        type: string
      - description: 操作权限点
        in: query
        name: op
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 是否有权限
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  type: boolean
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 检查全局操作权限
      tags:
      - 权限管理
  /api/rdb/can-do-node-op:
    get:
      consumes:
      - application/json
      description: 检查指定用户是否有权限在指定节点上执行操作
      parameters:
      - description: 用户名
        in: query
        name: username
        required: true
        type: string
      - description: 操作权限点
        in: query
        name: op
        required: true
        type: string
      - description: 节点ID
        format: int64
        in: query
        name: nid
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 是否有权限
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  type: boolean
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 检查节点操作权限
      tags:
      - 权限管理
  /api/rdb/can-do-node-ops:
    get:
      consumes:
      - application/json
      description: 批量检查指定用户在指定节点上的多个操作权限
      parameters:
      - description: 用户名
        in: query
        name: username
        required: true
        type: string
      - description: 操作权限点列表，逗号分隔
        in: query
        name: ops
        required: true
        type: string
      - description: 节点ID
        format: int64
        in: query
        name: nid
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 权限检查结果
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  additionalProperties:
                    type: boolean
                  type: object
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 批量检查节点操作权限
      tags:
      - 权限管理
  /api/rdb/configs/auth:
    get:
      consumes:
      - application/json
      description: 获取系统认证配置信息，包括密码策略、会话管理等设置
      produces:
      - application/json
      responses:
        "200":
          description: 认证配置信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.AuthConfig'
              type: object
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取认证配置
      tags:
      - 认证配置
    put:
      consumes:
      - application/json
      description: 更新系统认证配置信息，包括密码策略、会话管理等设置
      parameters:
      - description: 认证配置信息
        in: body
        name: config
        required: true
        schema:
          $ref: '#/definitions/models.AuthConfig'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 更新认证配置
      tags:
      - 认证配置
  /api/rdb/counter:
    get:
      consumes:
      - application/json
      description: 获取系统各项统计计数器，如登录次数等
      produces:
      - application/json
      responses:
        "200":
          description: 统计计数器
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  additionalProperties:
                    format: int64
                    type: integer
                  type: object
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取系统计数器
      tags:
      - 系统统计
  /api/rdb/log/login:
    get:
      consumes:
      - application/json
      description: 超级管理员查看所有用户的登录记录
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      - description: 用户名
        in: query
        name: username
        type: string
      - description: 开始时间戳
        in: query
        name: btime
        type: integer
      - description: 结束时间戳
        in: query
        name: etime
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 登录日志列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.LoginLogListResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取登录日志
      tags:
      - 日志管理
  /api/rdb/log/operation:
    get:
      consumes:
      - application/json
      description: 超级管理员查看所有类型资源的操作记录
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      - description: 搜索关键字
        in: query
        name: query
        type: string
      - description: 开始时间戳
        in: query
        name: btime
        type: integer
      - description: 结束时间戳
        in: query
        name: etime
        type: integer
      - description: 用户名
        in: query
        name: username
        type: string
      - description: 资源类型
        in: query
        name: res_type
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 操作日志列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.OperationLogListResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取操作日志
      tags:
      - 日志管理
  /api/rdb/node-cate-field/{id}:
    get:
      consumes:
      - application/json
      description: 获取指定节点分类字段的详细信息
      parameters:
      - description: 字段ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 节点分类字段详情
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.NodeCateField'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 字段不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取节点分类字段详情
      tags:
      - 节点分类字段管理
  /api/rdb/node-cate-fields:
    get:
      consumes:
      - application/json
      description: 根据分类获取节点分类的自定义字段列表
      parameters:
      - description: 节点分类标识
        in: query
        name: cate
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 节点分类字段列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.NodeCateField'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取节点分类字段列表
      tags:
      - 节点分类字段管理
    post:
      consumes:
      - application/json
      description: 为节点分类创建新的自定义字段
      parameters:
      - description: 字段信息
        in: body
        name: field
        required: true
        schema:
          $ref: '#/definitions/models.NodeCateField'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 创建节点分类字段
      tags:
      - 节点分类字段管理
  /api/rdb/node-cate/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定的节点分类
      parameters:
      - description: 节点分类ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 节点分类不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 删除节点分类
      tags:
      - 节点分类管理
    put:
      consumes:
      - application/json
      description: 修改指定节点分类的信息
      parameters:
      - description: 节点分类ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      - description: 节点分类信息
        in: body
        name: nodeCate
        required: true
        schema:
          $ref: '#/definitions/http.nodeCatePutForm'
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 节点分类不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 修改节点分类
      tags:
      - 节点分类管理
  /api/rdb/node-cates:
    get:
      consumes:
      - application/json
      description: 获取所有节点分类的列表
      produces:
      - application/json
      responses:
        "200":
          description: 节点分类列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.NodeCate'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取节点分类列表
      tags:
      - 节点分类管理
    post:
      consumes:
      - application/json
      description: 创建新的节点分类
      parameters:
      - description: 节点分类信息
        in: body
        name: nodeCate
        required: true
        schema:
          $ref: '#/definitions/http.nodeCatePostForm'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 创建节点分类
      tags:
      - 节点分类管理
  /api/rdb/node/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定的节点
      parameters:
      - description: 节点ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 节点不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 删除节点
      tags:
      - 节点管理
    get:
      consumes:
      - application/json
      description: 获取指定节点的详细信息，包括管理员信息
      parameters:
      - description: 节点ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 节点详情
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.Node'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 节点不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取节点详情
      tags:
      - 节点管理
    put:
      consumes:
      - application/json
      description: 修改指定节点的信息，包括名称、分类、备注、管理员等
      parameters:
      - description: 节点ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      - description: 节点信息
        in: body
        name: node
        required: true
        schema:
          $ref: '#/definitions/http.nodeForm'
      produces:
      - application/json
      responses:
        "200":
          description: 修改后的节点信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.Node'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 节点不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 修改节点
      tags:
      - 节点管理
  /api/rdb/node/{id}/fields:
    get:
      consumes:
      - application/json
      description: 获取指定节点的所有字段值
      parameters:
      - description: 节点ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 节点字段值列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.NodeFieldValue'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 节点不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取节点字段值
      tags:
      - 节点字段值管理
    put:
      consumes:
      - application/json
      description: 批量更新指定节点的字段值
      parameters:
      - description: 节点ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      - description: 字段值列表
        in: body
        name: fields
        required: true
        schema:
          items:
            $ref: '#/definitions/models.NodeFieldValue'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 节点不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 批量更新节点字段值
      tags:
      - 节点字段值管理
  /api/rdb/node/{id}/resources/bind:
    post:
      consumes:
      - application/json
      description: 将资源绑定到指定节点，支持按ID、UUID、标识批量绑定
      parameters:
      - description: 节点ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      - description: 资源绑定参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.resourceBindForm'
      produces:
      - application/json
      responses:
        "200":
          description: 绑定成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 节点不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 绑定资源到节点
      tags:
      - 资源管理
  /api/rdb/node/{id}/resources/cate-count:
    get:
      consumes:
      - application/json
      description: 获取指定节点下各类资源的数量统计
      parameters:
      - description: 节点ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 资源分类统计
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/http.ResourceCountByCate'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 节点不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取节点资源分类统计
      tags:
      - 资源统计
  /api/rdb/node/{id}/resources/unbind:
    post:
      consumes:
      - application/json
      description: 将资源从指定节点解绑
      parameters:
      - description: 节点ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      - description: 资源ID列表
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.idsForm'
      produces:
      - application/json
      responses:
        "200":
          description: 解绑成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 节点不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 解绑节点资源
      tags:
      - 资源管理
  /api/rdb/node/{id}/roles:
    delete:
      consumes:
      - application/json
      description: 删除指定节点下用户的角色权限
      parameters:
      - description: 节点ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      - description: 角色删除参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.roleUnderNodeDelForm'
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 节点不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 删除节点角色
      tags:
      - 节点角色管理
    get:
      consumes:
      - application/json
      description: 获取指定节点下的角色绑定列表，支持分页和用户筛选
      parameters:
      - description: 节点ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - description: 用户名筛选
        in: query
        name: username
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 角色列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  properties:
                    list:
                      items: {}
                      type: array
                    total:
                      format: int64
                      type: integer
                  type: object
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 节点不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取节点下的角色列表
      tags:
      - 节点角色管理
    post:
      consumes:
      - application/json
      description: 为指定节点的用户分配角色权限
      parameters:
      - description: 节点ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      - description: 角色分配参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.roleUnderNodeForm'
      produces:
      - application/json
      responses:
        "200":
          description: 分配成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 节点不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 为节点分配角色
      tags:
      - 节点角色管理
  /api/rdb/node/{id}/roles/try:
    delete:
      consumes:
      - application/json
      description: 测试当前用户是否有权限删除指定节点的角色
      parameters:
      - description: 节点ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 有权限
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 节点不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 测试节点角色删除权限
      tags:
      - 节点角色管理
  /api/rdb/nodes:
    get:
      consumes:
      - application/json
      description: 获取节点列表，支持按分类、内部节点、ID列表筛选
      parameters:
      - description: 节点分类
        in: query
        name: cate
        type: string
      - default: 0
        description: 是否包含内部节点，0-不包含，1-包含
        in: query
        name: inner
        type: integer
      - description: 节点ID列表，逗号分隔
        in: query
        name: ids
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 节点列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.Node'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取节点列表
      tags:
      - 节点管理
    post:
      consumes:
      - application/json
      description: 创建新的节点，支持创建租户节点和普通节点
      parameters:
      - description: 节点信息
        in: body
        name: node
        required: true
        schema:
          $ref: '#/definitions/http.nodeForm'
      produces:
      - application/json
      responses:
        "200":
          description: 创建的节点信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.Node'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 创建节点
      tags:
      - 节点管理
  /api/rdb/ops/global:
    get:
      consumes:
      - application/json
      description: 获取系统定义的页面权限配置
      produces:
      - application/json
      responses:
        "200":
          description: 全局权限配置
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/config.OpsSystem'
                  type: array
              type: object
      summary: 获取页面角色权限配置
      tags:
      - 系统管理
  /api/rdb/ops/local:
    get:
      consumes:
      - application/json
      description: 获取系统定义的资源权限配置
      produces:
      - application/json
      responses:
        "200":
          description: 本地权限配置
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/config.OpsSystem'
                  type: array
              type: object
      summary: 获取资源角色权限配置
      tags:
      - 系统管理
  /api/rdb/ping:
    get:
      consumes:
      - application/json
      description: 检查服务是否正常运行
      produces:
      - text/plain
      responses:
        "200":
          description: pong
          schema:
            type: string
      summary: 健康检查
      tags:
      - 系统管理
  /api/rdb/pwd-rules:
    get:
      consumes:
      - application/json
      description: 获取系统密码策略规则，用于前端显示密码要求
      produces:
      - application/json
      responses:
        "200":
          description: 密码规则列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    type: string
                  type: array
              type: object
      summary: 获取密码规则
      tags:
      - 系统管理
  /api/rdb/resoplogs:
    get:
      consumes:
      - application/json
      description: 查询具体某个资源的操作历史记录，一般用在资源详情页面
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - description: 开始时间戳
        format: int64
        in: query
        name: btime
        type: integer
      - description: 结束时间戳
        format: int64
        in: query
        name: etime
        type: integer
      - description: 资源类别
        in: query
        name: rescl
        type: string
      - description: 资源ID
        in: query
        name: resid
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 操作日志列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  properties:
                    list:
                      items:
                        $ref: '#/definitions/models.OperationLog'
                      type: array
                    total:
                      format: int64
                      type: integer
                  type: object
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取资源操作日志
      tags:
      - 操作日志
  /api/rdb/resources/bindings:
    get:
      consumes:
      - application/json
      description: 查看资源的绑定关系，主要用在页面上查看资源挂载的节点
      parameters:
      - description: 资源ID列表，逗号分隔
        in: query
        name: ids
        type: string
      - description: 资源UUID列表，逗号分隔
        in: query
        name: uuids
        type: string
      - description: 资源标识列表，逗号分隔
        in: query
        name: idents
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 资源绑定关系列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.ResourceBinding'
                  type: array
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取资源绑定关系
      tags:
      - 资源管理
  /api/rdb/resources/cate-count:
    get:
      consumes:
      - application/json
      description: 获取系统中所有资源按分类的数量统计
      produces:
      - application/json
      responses:
        "200":
          description: 资源分类统计
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/http.ResourceCountByCate'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取全部资源分类统计
      tags:
      - 资源统计
  /api/rdb/resources/note:
    put:
      consumes:
      - application/json
      description: 修改游离资源的备注信息，超级管理员或租户管理员有权限
      parameters:
      - description: 资源备注修改参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.resourceNotePutForm'
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 修改资源备注
      tags:
      - 资源管理
  /api/rdb/resources/note/try:
    put:
      consumes:
      - application/json
      description: 测试当前用户是否有权限修改游离资源的备注，超级管理员或租户管理员有权限
      parameters:
      - description: 租户路径
        in: query
        name: tenant
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 有权限
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 测试资源备注修改权限
      tags:
      - 资源管理
  /api/rdb/resources/orphan:
    get:
      consumes:
      - application/json
      description: 获取游离资源列表，即已经分配给某个租户但没有挂载在服务树的资源
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - description: 搜索关键词
        in: query
        name: query
        type: string
      - description: 批量搜索条件
        in: query
        name: batch
        type: string
      - default: ident
        description: 搜索字段
        in: query
        name: field
        type: string
      - description: 租户筛选
        in: query
        name: tenant
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 游离资源列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  properties:
                    list:
                      items:
                        $ref: '#/definitions/models.Resource'
                      type: array
                    tenant:
                      type: string
                    total:
                      format: int64
                      type: integer
                  type: object
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取游离资源列表
      tags:
      - 资源管理
  /api/rdb/resources/search:
    get:
      consumes:
      - application/json
      description: 根据批量条件和字段搜索资源
      parameters:
      - description: 批量搜索条件
        in: query
        name: batch
        type: string
      - default: ident
        description: 搜索字段
        in: query
        name: field
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 资源列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.Resource'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 搜索资源
      tags:
      - 资源管理
  /api/rdb/role/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定角色，会删除相关联的数据，只有超级管理员有权限
      parameters:
      - description: 角色ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 角色不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 删除角色
      tags:
      - 角色管理
    get:
      consumes:
      - application/json
      description: 获取指定角色的详细信息，包括角色信息和权限操作列表
      parameters:
      - description: 角色ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 角色详情
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  properties:
                    operations:
                      items:
                        type: string
                      type: array
                    role:
                      $ref: '#/definitions/models.Role'
                  type: object
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 角色不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取角色详情
      tags:
      - 角色管理
    put:
      consumes:
      - application/json
      description: 修改角色的基本信息，只有超级管理员有权限
      parameters:
      - description: 角色ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      - description: 角色信息
        in: body
        name: role
        required: true
        schema:
          $ref: '#/definitions/http.roleForm'
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 角色不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 修改角色
      tags:
      - 角色管理
  /api/rdb/role/{id}/users:
    get:
      consumes:
      - application/json
      description: 获取指定全局角色下绑定的用户列表，支持分页和搜索，只有管理员可以查看
      parameters:
      - description: 角色ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      - description: 搜索关键字
        in: query
        name: query
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 角色用户信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  properties:
                    list:
                      items:
                        $ref: '#/definitions/models.User'
                      type: array
                    role:
                      $ref: '#/definitions/models.Role'
                    total:
                      type: integer
                  type: object
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 角色不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取角色绑定的用户列表
      tags:
      - 角色管理
  /api/rdb/role/{id}/users/bind:
    put:
      consumes:
      - application/json
      description: 将指定用户绑定到全局角色，把某些用户设置为某个全局角色
      parameters:
      - description: 角色ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      - description: 用户ID列表
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.idsForm'
      produces:
      - application/json
      responses:
        "200":
          description: 绑定成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 角色不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 角色绑定用户
      tags:
      - 角色管理
  /api/rdb/role/{id}/users/unbind:
    put:
      consumes:
      - application/json
      description: 将指定用户从全局角色中解绑，把某些用户从某个全局角色中移除
      parameters:
      - description: 角色ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      - description: 用户ID列表
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.idsForm'
      produces:
      - application/json
      responses:
        "200":
          description: 解绑成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 角色不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 角色解绑用户
      tags:
      - 角色管理
  /api/rdb/roles:
    post:
      consumes:
      - application/json
      description: 创建新的角色，只有超级管理员有权限
      parameters:
      - description: 角色信息
        in: body
        name: role
        required: true
        schema:
          $ref: '#/definitions/http.roleForm'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功，返回角色ID
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  properties:
                    id:
                      format: int64
                      type: integer
                  type: object
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 创建角色
      tags:
      - 角色管理
  /api/rdb/roles/global:
    get:
      consumes:
      - application/json
      description: 获取系统中所有全局角色的列表，无权限限制
      produces:
      - application/json
      responses:
        "200":
          description: 全局角色列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.Role'
                  type: array
              type: object
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      summary: 获取页面角色列表
      tags:
      - 角色管理
  /api/rdb/roles/local:
    get:
      consumes:
      - application/json
      description: 获取系统中所有局部角色的列表，无权限限制
      produces:
      - application/json
      responses:
        "200":
          description: 局部角色列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.Role'
                  type: array
              type: object
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      summary: 获取资源角色列表
      tags:
      - 角色管理
  /api/rdb/root/teams/all:
    get:
      consumes:
      - application/json
      description: 超级管理员获取系统中所有团队的列表，支持分页和搜索
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      - description: 搜索关键字
        in: query
        name: query
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 团队列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.TeamListResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取所有团队列表(超管)
      tags:
      - 团队管理
  /api/rdb/self/password:
    put:
      consumes:
      - application/json
      description: 用户修改自己的登录密码
      parameters:
      - description: 密码修改信息
        in: body
        name: password
        required: true
        schema:
          $ref: '#/definitions/http.selfPasswordForm'
      produces:
      - application/json
      responses:
        "200":
          description: 密码修改成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 修改个人密码
      tags:
      - 个人中心
  /api/rdb/self/perms/global:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的全局权限操作列表
      produces:
      - application/json
      responses:
        "200":
          description: 用户权限操作列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  additionalProperties: true
                  type: object
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取用户全局权限
      tags:
      - 个人中心
  /api/rdb/self/profile:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的个人信息
      produces:
      - application/json
      responses:
        "200":
          description: 个人信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.User'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取个人信息
      tags:
      - 个人中心
    put:
      consumes:
      - application/json
      description: 更新当前登录用户的个人信息
      parameters:
      - description: 个人信息
        in: body
        name: profile
        required: true
        schema:
          $ref: '#/definitions/http.selfProfileForm'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 更新个人信息
      tags:
      - 个人中心
  /api/rdb/self/token:
    get:
      consumes:
      - application/json
      description: 获取当前用户的所有API Token
      produces:
      - application/json
      responses:
        "200":
          description: Token列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.UserToken'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取个人Token列表
      tags:
      - 个人中心
    post:
      consumes:
      - application/json
      description: 为当前用户创建新的API Token
      produces:
      - application/json
      responses:
        "200":
          description: 新创建的Token
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.UserToken'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 创建个人Token
      tags:
      - 个人中心
    put:
      consumes:
      - application/json
      description: 重置指定的API Token
      parameters:
      - description: Token信息
        in: body
        name: token
        required: true
        schema:
          $ref: '#/definitions/http.selfTokenForm'
      produces:
      - application/json
      responses:
        "200":
          description: 重置后的Token
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.UserToken'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 重置个人Token
      tags:
      - 个人中心
  /api/rdb/team/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定的团队
      parameters:
      - description: 团队ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 团队不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 删除团队
      tags:
      - 团队管理
    get:
      consumes:
      - application/json
      description: 获取指定团队的详细信息，包括团队成员列表
      parameters:
      - description: 团队ID
        in: path
        name: id
        required: true
        type: integer
      - description: 搜索关键字
        in: query
        name: query
        type: string
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 团队详情和成员列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.TeamDetailResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 团队不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取团队详情
      tags:
      - 团队管理
    put:
      consumes:
      - application/json
      description: 更新指定团队的信息
      parameters:
      - description: 团队ID
        in: path
        name: id
        required: true
        type: integer
      - description: 团队信息
        in: body
        name: team
        required: true
        schema:
          $ref: '#/definitions/http.teamForm'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 团队不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 更新团队信息
      tags:
      - 团队管理
  /api/rdb/team/{id}/users/bind:
    put:
      consumes:
      - application/json
      description: 为团队添加管理员和普通成员
      parameters:
      - description: 团队ID
        in: path
        name: id
        required: true
        type: integer
      - description: 用户绑定信息
        in: body
        name: users
        required: true
        schema:
          $ref: '#/definitions/http.teamUserBindForm'
      produces:
      - application/json
      responses:
        "200":
          description: 绑定成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 团队不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 绑定团队成员
      tags:
      - 团队管理
  /api/rdb/team/{id}/users/unbind:
    put:
      consumes:
      - application/json
      description: 从团队中移除指定的成员
      parameters:
      - description: 团队ID
        in: path
        name: id
        required: true
        type: integer
      - description: 用户解绑信息
        in: body
        name: users
        required: true
        schema:
          $ref: '#/definitions/http.teamUserUnbindForm'
      produces:
      - application/json
      responses:
        "200":
          description: 解绑成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 团队不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 解绑团队成员
      tags:
      - 团队管理
  /api/rdb/teams:
    post:
      consumes:
      - application/json
      description: 创建新的团队
      parameters:
      - description: 团队信息
        in: body
        name: team
        required: true
        schema:
          $ref: '#/definitions/http.teamForm'
      produces:
      - application/json
      responses:
        "200":
          description: 创建的团队信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.Team'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 创建团队
      tags:
      - 团队管理
  /api/rdb/teams/all:
    get:
      consumes:
      - application/json
      description: 获取系统中所有团队的列表，支持分页和搜索
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      - description: 搜索关键字
        in: query
        name: query
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 团队列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.TeamListResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取所有团队列表
      tags:
      - 团队管理
  /api/rdb/teams/mine:
    get:
      consumes:
      - application/json
      description: 获取当前用户所属的团队列表，支持分页和搜索
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      - description: 搜索关键字
        in: query
        name: query
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 我的团队列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.TeamListResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取我的团队列表
      tags:
      - 团队管理
  /api/rdb/tree:
    get:
      consumes:
      - application/json
      description: 获取用户有权限的完整节点树，支持搜索功能
      parameters:
      - description: 搜索关键词，支持节点名称和资源标识搜索
        in: query
        name: query
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 节点树列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.Node'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取完整节点树
      tags:
      - 节点树管理
  /api/rdb/tree/orgs:
    get:
      consumes:
      - application/json
      description: 获取用户有权限的节点树，只展示到组织级别
      produces:
      - application/json
      responses:
        "200":
          description: 组织级节点树
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.Node'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取组织级节点树
      tags:
      - 节点树管理
  /api/rdb/tree/projs:
    get:
      consumes:
      - application/json
      description: 获取用户有权限的节点树，只展示到项目级别
      produces:
      - application/json
      responses:
        "200":
          description: 项目级节点树
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.Node'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取项目级节点树
      tags:
      - 节点树管理
  /api/rdb/user/{id}:
    delete:
      consumes:
      - application/json
      description: 超级管理员删除指定用户
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 删除用户
      tags:
      - 用户管理
  /api/rdb/user/{id}/disable:
    put:
      consumes:
      - application/json
      description: 超级管理员禁用指定用户
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 禁用成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 禁用用户
      tags:
      - 用户管理
  /api/rdb/user/{id}/enable:
    put:
      consumes:
      - application/json
      description: 超级管理员启用指定用户
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 启用成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 启用用户
      tags:
      - 用户管理
  /api/rdb/user/{id}/password:
    put:
      consumes:
      - application/json
      description: 超级管理员重置指定用户的密码
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      - description: 新密码
        in: body
        name: password
        required: true
        schema:
          $ref: '#/definitions/http.userPasswordForm'
      produces:
      - application/json
      responses:
        "200":
          description: 密码重置成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 重置用户密码
      tags:
      - 用户管理
  /api/rdb/user/{id}/profile:
    get:
      consumes:
      - application/json
      description: 超级管理员获取指定用户的详细信息
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 用户详情
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.User'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取用户详情
      tags:
      - 用户管理
    put:
      consumes:
      - application/json
      description: 超级管理员更新指定用户的信息
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      - description: 用户信息
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/http.userProfileForm'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 更新用户信息
      tags:
      - 用户管理
  /api/rdb/users:
    get:
      consumes:
      - application/json
      - application/json
      description: |-
        获取系统用户列表，支持分页和搜索
        获取系统用户列表，支持分页和搜索
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 0
        description: 偏移量
        in: query
        name: offset
        type: integer
      - description: 搜索关键字
        in: query
        name: query
        type: string
      - description: 查询条件
        in: query
        name: conditions
        type: string
      - description: 组织
        in: query
        name: org
        type: string
      - description: 用户ID列表，逗号分隔
        in: query
        name: ids
        type: string
      - description: 每页数量，默认20
        in: query
        name: limit
        type: integer
      - description: 页码，默认1
        in: query
        name: p
        type: integer
      - description: 搜索关键词
        in: query
        name: query
        type: string
      - description: 组织筛选
        in: query
        name: org
        type: string
      - description: 用户ID列表，逗号分隔
        in: query
        name: ids
        type: string
      produces:
      - application/json
      - application/json
      responses:
        "200":
          description: 用户列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  properties:
                    list:
                      items:
                        $ref: '#/definitions/models.User'
                      type: array
                    total:
                      format: int64
                      type: integer
                  type: object
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 获取用户列表
      tags:
      - 用户管理
      - 用户管理
    post:
      consumes:
      - application/json
      - application/json
      description: |-
        超级管理员创建新用户
        超级管理员创建新用户
      parameters:
      - description: 用户信息
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/http.userProfileForm'
      - description: 用户信息
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/http.userProfileForm'
      produces:
      - application/json
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 创建用户
      tags:
      - 用户管理
      - 用户管理
  /api/rdb/users/invite:
    get:
      consumes:
      - application/json
      description: 生成用户注册邀请Token，用于用户自助注册
      produces:
      - application/json
      responses:
        "200":
          description: 邀请Token
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  type: string
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      - CookieAuth: []
      summary: 生成用户邀请Token
      tags:
      - 用户管理
    post:
      consumes:
      - application/json
      description: 使用邀请Token进行用户自助注册
      parameters:
      - description: 用户注册信息
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/http.userInviteForm'
      produces:
      - application/json
      responses:
        "200":
          description: 注册成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      summary: 通过邀请Token注册用户
      tags:
      - 用户管理
  /v1/rdb/container/sync:
    post:
      consumes:
      - application/json
      description: 同步容器资源信息，用于容器管理系统数据同步
      parameters:
      - description: 容器同步参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.containerSyncForm'
      produces:
      - application/json
      responses:
        "200":
          description: 同步成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ServiceAuth: []
      summary: 同步容器资源
      tags:
      - V1接口
  /v1/rdb/containers/bind:
    post:
      consumes:
      - application/json
      description: 批量绑定容器资源到节点
      parameters:
      - description: 容器资源列表
        in: body
        name: body
        required: true
        schema:
          items:
            $ref: '#/definitions/http.v1ContainersRegisterItem'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: 绑定成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ServiceAuth: []
      summary: 绑定容器资源
      tags:
      - V1接口
  /v1/rdb/get-user-by-uuid:
    get:
      consumes:
      - application/json
      description: 根据用户UUID获取用户详细信息
      parameters:
      - description: 用户UUID
        in: query
        name: uuid
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 用户信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.User'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ServiceAuth: []
      summary: 根据UUID获取用户(V1接口)
      tags:
      - V1接口
  /v1/rdb/get-users-by-uuids:
    get:
      consumes:
      - application/json
      description: 根据用户UUID列表批量获取用户信息
      parameters:
      - description: 用户UUID列表，逗号分隔
        in: query
        name: uuids
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 用户列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.User'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ServiceAuth: []
      summary: 批量根据UUID获取用户(V1接口)
      tags:
      - V1接口
  /v1/rdb/node-include-trash/{id}:
    get:
      consumes:
      - application/json
      description: 获取节点信息，包括已删除的节点，主要用于补全信息
      parameters:
      - description: 节点ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 节点信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.Node'
              type: object
        "404":
          description: 节点不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ServiceAuth: []
      summary: 获取节点信息(包含回收站)
      tags:
      - V1接口
  /v1/rdb/node/{id}/projs:
    get:
      consumes:
      - application/json
      description: 根据指定节点ID获取项目级别的节点树
      parameters:
      - description: 节点ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 项目节点树
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.Node'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 节点不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ServiceAuth: []
      summary: 根据节点ID获取项目树(V1接口)
      tags:
      - V1接口
  /v1/rdb/node/{id}/resources:
    get:
      consumes:
      - application/json
      description: 获取指定节点下的所有资源，不受用户权限限制，主要供内部服务调用
      parameters:
      - description: 节点ID
        format: int64
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 资源列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.Resource'
                  type: array
              type: object
        "404":
          description: 节点不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ServiceAuth: []
      summary: 获取节点下的所有资源(V1接口)
      tags:
      - V1接口
  /v1/rdb/resources/register:
    post:
      consumes:
      - application/json
      description: 第三方系统注册资源到RDB，支持批量注册
      parameters:
      - description: 资源注册列表
        in: body
        name: body
        required: true
        schema:
          items:
            $ref: '#/definitions/models.ResourceRegisterItem'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: 注册成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ServiceAuth: []
      summary: 注册资源(V1接口)
      tags:
      - V1接口
  /v1/rdb/resources/unregister:
    post:
      consumes:
      - application/json
      description: 第三方系统注销资源，从RDB中删除资源及其绑定关系
      parameters:
      - description: 资源UUID列表
        in: body
        name: body
        required: true
        schema:
          items:
            type: string
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: 注销成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ServiceAuth: []
      summary: 注销资源(V1接口)
      tags:
      - V1接口
  /v1/rdb/table/sync/role-global-user:
    get:
      consumes:
      - application/json
      description: 获取所有全局角色与用户的绑定关系，用于数据同步
      produces:
      - application/json
      responses:
        "200":
          description: 角色用户关系列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.RoleGlobalUser'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ServiceAuth: []
      summary: 获取全局角色用户关系
      tags:
      - V1接口
  /v1/rdb/tree:
    get:
      consumes:
      - application/json
      description: 根据用户名和分类获取用户有权限的节点树
      parameters:
      - description: 用户名
        in: query
        name: username
        required: true
        type: string
      - description: 节点分类
        in: query
        name: cate
        required: true
        type: string
      - default: 0
        description: 是否只返回指定分类
        in: query
        name: onlyCate
        type: integer
      - default: -1
        description: 父节点ID
        format: int64
        in: query
        name: pid
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 用户分类节点树
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.Node'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ServiceAuth: []
      summary: 获取用户分类树(V1接口)
      tags:
      - V1接口
  /v1/rdb/tree/projs:
    get:
      consumes:
      - application/json
      description: 根据用户名获取用户有权限的项目级节点树
      parameters:
      - description: 用户名
        in: query
        name: username
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 用户项目节点树
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.Node'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ServiceAuth: []
      summary: 获取用户项目树(V1接口)
      tags:
      - V1接口
  /v1/rdb/users:
    get:
      consumes:
      - application/json
      description: 获取用户列表，支持分页、搜索、组织筛选
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - description: 搜索关键词
        in: query
        name: query
        type: string
      - description: 组织筛选
        in: query
        name: org
        type: string
      - description: 用户ID列表，逗号分隔
        in: query
        name: ids
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 用户列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  properties:
                    list:
                      items:
                        $ref: '#/definitions/models.User'
                      type: array
                    total:
                      format: int64
                      type: integer
                  type: object
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ServiceAuth: []
      summary: 获取用户列表(V1接口)
      tags:
      - V1接口
securityDefinitions:
  ApiKeyAuth:
    description: User token for API authentication
    in: header
    name: X-User-Token
    type: apiKey
  CookieAuth:
    description: Session cookie for web authentication
    in: cookie
    name: ecmc-sid
    type: apiKey
swagger: "2.0"
