#!/bin/bash

# 测试修复后的用户组功能

BASE_URL="http://localhost:8000"
TOKEN="41a7ae8949f77c3deb3a351bd6927734"

echo "=== 测试修复后的用户组功能 ==="

# 1. 创建测试用户组
echo "1. 创建测试用户组..."
CREATE_RESPONSE=$(curl -s -X 'POST' \
  "${BASE_URL}/api/rdb/teams" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}" \
  -H 'Content-Type: application/json' \
  -d '{
  "ident": "test-group-fixed",
  "name": "修复测试用户组",
  "note": "这是一个修复后的测试用户组",
  "mgmt": 0
}')

echo "创建用户组响应: $CREATE_RESPONSE"

echo -e "\n等待5秒让用户组创建事件处理完成..."
sleep 5

# 2. 验证用户组是否在JumpServer中创建成功
echo -e "\n2. 验证JumpServer中的用户组..."
JS_SEARCH=$(curl -s -H "Authorization: Token e7261a6a44d0a3991dbd77c9d308be3e887340b6" \
  -H "X-JMS-ORG: 00000000-0000-0000-0000-000000000002" \
  "http://**********:8090/api/v1/users/groups/?search=修复测试用户组&offset=0&limit=15&display=1")

echo "JumpServer搜索结果: $JS_SEARCH"

# 3. 获取团队ID用于删除
echo -e "\n3. 获取团队信息..."
TEAM_LIST=$(curl -s -X 'GET' \
  "${BASE_URL}/api/rdb/teams?limit=10&p=1" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}")

# 简单提取团队ID
TEAM_ID=$(echo "$TEAM_LIST" | grep -o '"id":[0-9]*' | tail -1 | cut -d':' -f2)
echo "团队ID: $TEAM_ID"

if [ -z "$TEAM_ID" ]; then
    echo "无法获取团队ID，跳过删除测试"
else
    # 4. 删除测试用户组
    echo -e "\n4. 删除测试用户组..."
    DELETE_RESPONSE=$(curl -s -X 'DELETE' \
      "${BASE_URL}/api/rdb/team/${TEAM_ID}" \
      -H 'accept: application/json' \
      -H "X-User-Token: ${TOKEN}")

    echo "删除用户组响应: $DELETE_RESPONSE"

    echo -e "\n等待5秒让用户组删除事件处理完成..."
    sleep 5

    # 5. 验证用户组是否从JumpServer中删除
    echo -e "\n5. 验证JumpServer中的用户组是否已删除..."
    JS_SEARCH_AFTER=$(curl -s -H "Authorization: Token e7261a6a44d0a3991dbd77c9d308be3e887340b6" \
      -H "X-JMS-ORG: 00000000-0000-0000-0000-000000000002" \
      "http://**********:8090/api/v1/users/groups/?search=修复测试用户组&offset=0&limit=15&display=1")

    echo "删除后JumpServer搜索结果: $JS_SEARCH_AFTER"
fi

echo -e "\n=== 用户组功能测试完成 ==="
echo "请检查jumpserver-sync日志确认事件处理是否正确"
