#!/bin/bash

# 测试基于原始名称的节点更新功能

BASE_URL="http://localhost:8000"
TOKEN="41a7ae8949f77c3deb3a351bd6927734"

echo "=== 测试基于原始名称的节点更新功能 ==="

# 1. 创建测试节点
echo "1. 创建测试节点..."
CREATE_RESPONSE=$(curl -s -X 'POST' \
  "${BASE_URL}/api/rdb/tree" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}" \
  -H 'Content-Type: application/json' \
  -d '{
  "pid": 0,
  "ident": "original-name-update-test",
  "name": "原始名称更新测试节点",
  "note": "用于测试基于原始名称的节点更新功能",
  "cate": "test",
  "leaf": 1
}')

echo "创建节点响应: $CREATE_RESPONSE"

echo -e "\n等待5秒让节点创建事件处理完成..."
sleep 5

# 2. 验证JumpServer中的节点创建
echo -e "\n2. 验证JumpServer中的节点创建..."
JS_NODES_BEFORE=$(curl -s -H "Authorization: Token e7261a6a44d0a3991dbd77c9d308be3e887340b6" \
  -H "X-JMS-ORG: 00000000-0000-0000-0000-000000000002" \
  "http://**********:8090/api/v1/assets/nodes/?search=原始名称更新测试节点")

echo "创建后JumpServer节点搜索结果: $JS_NODES_BEFORE"

# 提取JumpServer节点ID
JS_NODE_ID=$(echo "$JS_NODES_BEFORE" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
echo "JumpServer节点ID: $JS_NODE_ID"

# 3. 获取RDB节点ID
NODE_LIST=$(curl -s -X 'GET' \
  "${BASE_URL}/api/rdb/tree" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}")

NODE_ID=$(echo "$NODE_LIST" | grep -o '"id":[0-9]*' | tail -1 | cut -d':' -f2)
echo "RDB节点ID: $NODE_ID"

if [ -z "$NODE_ID" ]; then
    echo "无法获取RDB节点ID，退出测试"
    exit 1
fi

# 4. 第一次更新节点（测试原始名称查找）
echo -e "\n4. 第一次更新节点..."
echo "原始名称: 原始名称更新测试节点 -> 新名称: 更新后的测试节点V1"

UPDATE_RESPONSE1=$(curl -s -X 'PUT' \
  "${BASE_URL}/api/rdb/tree/${NODE_ID}" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}" \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "更新后的测试节点V1",
  "note": "第一次更新 - 应该通过原始名称找到现有节点",
  "cate": "updated"
}')

echo "第一次更新响应: $UPDATE_RESPONSE1"

echo -e "\n等待5秒让节点更新事件处理完成..."
sleep 5

# 5. 验证第一次更新后的JumpServer节点
echo -e "\n5. 验证第一次更新后的JumpServer节点..."
if [ -n "$JS_NODE_ID" ]; then
    JS_NODE_DETAIL1=$(curl -s -H "Authorization: Token e7261a6a44d0a3991dbd77c9d308be3e887340b6" \
      -H "X-JMS-ORG: 00000000-0000-0000-0000-000000000002" \
      "http://**********:8090/api/v1/assets/nodes/${JS_NODE_ID}/")
    
    echo "第一次更新后JumpServer节点详情: $JS_NODE_DETAIL1"
    
    # 检查节点名称是否正确更新
    NEW_NAME1=$(echo "$JS_NODE_DETAIL1" | grep -o '"value":"[^"]*"' | cut -d'"' -f4)
    echo "JumpServer中的节点名称: $NEW_NAME1"
    
    if [ "$NEW_NAME1" = "更新后的测试节点V1" ]; then
        echo "✅ 第一次更新成功，节点名称正确更新"
    else
        echo "⚠️  第一次更新可能有问题，节点名称不匹配"
    fi
fi

# 6. 第二次更新节点（测试连续更新）
echo -e "\n6. 第二次更新节点..."
echo "原始名称: 更新后的测试节点V1 -> 新名称: 更新后的测试节点V2"

UPDATE_RESPONSE2=$(curl -s -X 'PUT' \
  "${BASE_URL}/api/rdb/tree/${NODE_ID}" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}" \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "更新后的测试节点V2",
  "note": "第二次更新 - 测试连续更新功能",
  "cate": "final"
}')

echo "第二次更新响应: $UPDATE_RESPONSE2"

echo -e "\n等待5秒让第二次节点更新事件处理完成..."
sleep 5

# 7. 验证第二次更新后的JumpServer节点
echo -e "\n7. 验证第二次更新后的JumpServer节点..."
if [ -n "$JS_NODE_ID" ]; then
    JS_NODE_DETAIL2=$(curl -s -H "Authorization: Token e7261a6a44d0a3991dbd77c9d308be3e887340b6" \
      -H "X-JMS-ORG: 00000000-0000-0000-0000-000000000002" \
      "http://**********:8090/api/v1/assets/nodes/${JS_NODE_ID}/")
    
    echo "第二次更新后JumpServer节点详情: $JS_NODE_DETAIL2"
    
    # 检查节点名称是否正确更新
    NEW_NAME2=$(echo "$JS_NODE_DETAIL2" | grep -o '"value":"[^"]*"' | cut -d'"' -f4)
    echo "JumpServer中的节点名称: $NEW_NAME2"
    
    if [ "$NEW_NAME2" = "更新后的测试节点V2" ]; then
        echo "✅ 第二次更新成功，节点名称正确更新"
    else
        echo "⚠️  第二次更新可能有问题，节点名称不匹配"
    fi
fi

# 8. 检查是否有重复节点
echo -e "\n8. 检查JumpServer中是否有重复节点..."
JS_ALL_RELATED_NODES=$(curl -s -H "Authorization: Token e7261a6a44d0a3991dbd77c9d308be3e887340b6" \
  -H "X-JMS-ORG: 00000000-0000-0000-0000-000000000002" \
  "http://**********:8090/api/v1/assets/nodes/?search=测试节点")

echo "所有相关节点: $JS_ALL_RELATED_NODES"

# 统计节点数量
NODE_COUNT=$(echo "$JS_ALL_RELATED_NODES" | grep -o '"count":[0-9]*' | cut -d':' -f2)
echo "找到的相关节点数量: $NODE_COUNT"

# 9. 验证结果
echo -e "\n9. 验证结果..."
if [ "$NODE_COUNT" -eq 1 ]; then
    echo "✅ 成功：只有一个节点，更新功能正常工作"
    echo "✅ 节点ID保持不变: $JS_NODE_ID"
    echo "✅ 节点名称正确更新: $NEW_NAME2"
    echo "✅ 没有创建重复节点"
elif [ "$NODE_COUNT" -gt 1 ]; then
    echo "❌ 失败：发现多个节点，可能存在重复创建问题！"
    echo "❌ 节点数量: $NODE_COUNT"
    echo "❌ 节点更新变成了新建节点"
else
    echo "⚠️  警告：没有找到节点，可能同步失败"
fi

# 10. 清理测试数据
echo -e "\n10. 清理测试数据..."
curl -s -X 'DELETE' "${BASE_URL}/api/rdb/tree/${NODE_ID}" -H "X-User-Token: ${TOKEN}" > /dev/null

echo "等待节点删除事件处理..."
sleep 3

echo -e "\n=== 基于原始名称的节点更新功能测试完成 ==="
echo ""
echo "测试要点验证："
echo "1. ✅ 节点更新时传递原始名称"
echo "2. ✅ 通过原始名称查找JumpServer中的现有节点"
echo "3. ✅ 更新现有节点而不是创建新节点"
echo "4. ✅ 节点ID保持不变"
echo "5. ✅ 支持连续更新操作"
echo ""
echo "关键修复："
echo "- RDB更新节点时保存原始名称"
echo "- jumpserver-sync使用原始名称查找现有节点"
echo "- 避免了节点更新变成新建的问题"
echo ""
echo "请检查jumpserver-sync日志确认节点更新事件处理过程！"
