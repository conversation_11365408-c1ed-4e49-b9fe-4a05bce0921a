#!/bin/bash

# 测试密码重置功能的脚本

BASE_URL="http://localhost:8000"
TOKEN="41a7ae8949f77c3deb3a351bd6927734"

echo "=== 测试密码重置功能 ==="

# 1. 创建测试用户
echo "1. 创建测试用户..."
CREATE_RESPONSE=$(curl -s -X 'POST' \
  "${BASE_URL}/api/rdb/users" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}" \
  -H 'Content-Type: application/json' \
  -d '{
  "dispname": "密码测试用户",
  "email": "<EMAIL>",
  "im": "18144444444",
  "is_root": 0,
  "leader_id": 0,
  "password": "InitialPassword123!",
  "status": 0,
  "typ": 0,
  "username": "pwdtestuser"
}')

echo "创建用户响应: $CREATE_RESPONSE"

echo -e "\n等待3秒让用户创建事件处理完成..."
sleep 3

# 2. 获取用户ID
echo -e "\n2. 获取用户信息..."
USER_LIST=$(curl -s -X 'GET' \
  "${BASE_URL}/api/rdb/users?limit=10&p=1" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}")

# 简单提取用户ID（实际应该解析JSON）
USER_ID=$(echo "$USER_LIST" | grep -o '"id":[0-9]*' | tail -1 | cut -d':' -f2)
echo "用户ID: $USER_ID"

if [ -z "$USER_ID" ]; then
    echo "无法获取用户ID，退出测试"
    exit 1
fi

# 3. 重置密码
echo -e "\n3. 重置用户密码..."
PASSWORD_RESPONSE=$(curl -s -X 'PUT' \
  "${BASE_URL}/api/rdb/user/${USER_ID}/password" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}" \
  -H 'Content-Type: application/json' \
  -d '{
  "password": "NewPassword456!"
}')

echo "密码重置响应: $PASSWORD_RESPONSE"

echo -e "\n等待3秒让密码重置事件处理完成..."
sleep 3

# 4. 再次重置密码
echo -e "\n4. 再次重置密码..."
PASSWORD_RESPONSE2=$(curl -s -X 'PUT' \
  "${BASE_URL}/api/rdb/user/${USER_ID}/password" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}" \
  -H 'Content-Type: application/json' \
  -d '{
  "password": "FinalPassword789!"
}')

echo "第二次密码重置响应: $PASSWORD_RESPONSE2"

echo -e "\n等待3秒让密码重置事件处理完成..."
sleep 3

# 5. 清理：删除测试用户
echo -e "\n5. 删除测试用户..."
DELETE_RESPONSE=$(curl -s -X 'DELETE' \
  "${BASE_URL}/api/rdb/user/${USER_ID}" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}")

echo "删除用户响应: $DELETE_RESPONSE"

echo -e "\n等待3秒让删除事件处理完成..."
sleep 3

echo -e "\n=== 密码重置测试完成 ==="
echo "请检查jumpserver-sync日志确认密码重置事件是否正确处理"
