#!/bin/bash

# 测试修复后的团队成员移除功能

BASE_URL="http://localhost:8000"
TOKEN="41a7ae8949f77c3deb3a351bd6927734"

echo "=== 测试修复后的团队成员移除功能 ==="

# 1. 创建测试团队
echo "1. 创建测试团队..."
CREATE_TEAM_RESPONSE=$(curl -s -X 'POST' \
  "${BASE_URL}/api/rdb/teams" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}" \
  -H 'Content-Type: application/json' \
  -d '{
  "ident": "remove-test-team",
  "name": "移除测试团队",
  "note": "用于测试成员移除功能的团队",
  "mgmt": 1
}')

echo "创建团队响应: $CREATE_TEAM_RESPONSE"

echo -e "\n等待3秒让团队创建事件处理完成..."
sleep 3

# 2. 创建测试用户
echo -e "\n2. 创建测试用户..."
CREATE_USER_RESPONSE=$(curl -s -X 'POST' \
  "${BASE_URL}/api/rdb/users" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}" \
  -H 'Content-Type: application/json' \
  -d '{
  "dispname": "移除测试用户",
  "email": "<EMAIL>",
  "im": "18166666666",
  "is_root": 0,
  "leader_id": 0,
  "password": "RemoveTest123!",
  "status": 0,
  "typ": 0,
  "username": "removetest"
}')

echo "创建用户响应: $CREATE_USER_RESPONSE"

echo -e "\n等待3秒让用户创建事件处理完成..."
sleep 3

# 3. 获取团队ID和用户ID
echo -e "\n3. 获取团队和用户信息..."
TEAM_LIST=$(curl -s -X 'GET' \
  "${BASE_URL}/api/rdb/teams?limit=10&p=1" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}")

USER_LIST=$(curl -s -X 'GET' \
  "${BASE_URL}/api/rdb/users?limit=10&p=1" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}")

# 简单提取ID
TEAM_ID=$(echo "$TEAM_LIST" | grep -o '"id":[0-9]*' | tail -1 | cut -d':' -f2)
USER_ID=$(echo "$USER_LIST" | grep -o '"id":[0-9]*' | tail -1 | cut -d':' -f2)

echo "团队ID: $TEAM_ID"
echo "用户ID: $USER_ID"

if [ -z "$TEAM_ID" ] || [ -z "$USER_ID" ]; then
    echo "无法获取团队ID或用户ID，退出测试"
    exit 1
fi

# 4. 添加用户到团队
echo -e "\n4. 添加用户到团队..."
BIND_RESPONSE=$(curl -s -X 'PUT' \
  "${BASE_URL}/api/rdb/team/${TEAM_ID}/users/bind" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}" \
  -H 'Content-Type: application/json' \
  -d "{
  \"admin_ids\": [],
  \"member_ids\": [${USER_ID}]
}")

echo "绑定用户响应: $BIND_RESPONSE"

echo -e "\n等待5秒让团队添加成员事件处理完成..."
sleep 5

# 5. 验证JumpServer中的用户组成员
echo -e "\n5. 验证JumpServer中的用户组成员..."
JS_GROUP_SEARCH=$(curl -s -H "Authorization: Token e7261a6a44d0a3991dbd77c9d308be3e887340b6" \
  -H "X-JMS-ORG: 00000000-0000-0000-0000-000000000002" \
  "http://10.1.4.213:8090/api/v1/users/groups/?search=移除测试团队&offset=0&limit=15&display=1")

echo "添加后JumpServer用户组搜索结果: $JS_GROUP_SEARCH"

# 6. 从团队移除用户（重点测试）
echo -e "\n6. 从团队移除用户..."
UNBIND_RESPONSE=$(curl -s -X 'PUT' \
  "${BASE_URL}/api/rdb/team/${TEAM_ID}/users/unbind" \
  -H 'accept: application/json' \
  -H "X-User-Token: ${TOKEN}" \
  -H 'Content-Type: application/json' \
  -d "{
  \"user_ids\": [${USER_ID}]
}")

echo "解绑用户响应: $UNBIND_RESPONSE"

echo -e "\n等待5秒让团队移除成员事件处理完成..."
sleep 5

# 7. 再次验证JumpServer中的用户组成员
echo -e "\n7. 验证移除后的JumpServer用户组成员..."
JS_GROUP_SEARCH_AFTER=$(curl -s -H "Authorization: Token e7261a6a44d0a3991dbd77c9d308be3e887340b6" \
  -H "X-JMS-ORG: 00000000-0000-0000-0000-000000000002" \
  "http://10.1.4.213:8090/api/v1/users/groups/?search=移除测试团队&offset=0&limit=15&display=1")

echo "移除后JumpServer用户组搜索结果: $JS_GROUP_SEARCH_AFTER"

# 8. 清理：删除测试数据
echo -e "\n8. 清理测试数据..."
curl -s -X 'DELETE' "${BASE_URL}/api/rdb/team/${TEAM_ID}" -H "X-User-Token: ${TOKEN}"
curl -s -X 'DELETE' "${BASE_URL}/api/rdb/user/${USER_ID}" -H "X-User-Token: ${TOKEN}"

echo -e "\n=== 团队成员移除功能测试完成 ==="
echo "请检查jumpserver-sync日志确认以下内容："
echo "- team.remove_member事件是否正确处理"
echo "- operation_user_ids字段是否包含被移除的用户ID"
echo "- JumpServer用户组中的用户是否被正确移除"
