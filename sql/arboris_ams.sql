set names utf8;

drop database if exists arboris_ams;
create database arboris_ams;
use arboris_ams;

CREATE TABLE `host` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `sn` char(128) NOT NULL DEFAULT '',
  `ip` char(15) NOT NULL,
  `ident` varchar(128) NOT NULL DEFAULT '',
  `name` varchar(128) NOT NULL DEFAULT '',
  `os_version` varchar(255) NOT NULL DEFAULT '',
  `kernel_version` varchar(255) NOT NULL DEFAULT '',
  `cpu_model` varchar(255) NOT NULL DEFAULT '',
  `cpu` varchar(255) NOT NULL DEFAULT '',
  `mem` varchar(255) NOT NULL DEFAULT '',
  `disk` varchar(255) NOT NULL DEFAULT '',
  `note` varchar(255) NOT NULL DEFAULT 'different with resource note',
  `cate` varchar(32) NOT NULL COMMENT 'host,vm,container,switch',
  `tenant` varchar(128) NOT NULL DEFAULT '',
  `clock` bigint NOT NULL COMMENT 'heartbeat timestamp',
  `gpu` varchar(255) NOT NULL DEFAULT '8',
  `gpu_model` varchar(255) NOT NULL DEFAULT '',
  `model` varchar(255) NOT NULL DEFAULT '""',
  `idc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '""',
  `zone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '""',
  `rack` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '""',
  `manufacturer` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '""',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip` (`ip`),
  UNIQUE KEY `ident` (`ident`),
  KEY `sn` (`sn`),
  KEY `name` (`name`),
  KEY `tenant` (`tenant`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

CREATE TABLE `host_field`
(
    `id`             int unsigned  not null AUTO_INCREMENT,
    `field_ident`    varchar(255)  not null comment 'english identity',
    `field_name`     varchar(255)  not null comment 'chinese name',
    `field_type`     varchar(64)   not null,
    `field_required` tinyint(1)    not null default 0,
    `field_extra`    varchar(2048) not null default '',
    `field_cate`     varchar(255)  not null default 'Default',
    PRIMARY KEY (`id`),
    KEY (`field_cate`, `field_ident`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

CREATE TABLE `host_field_value`
(
    `id`          int unsigned  not null AUTO_INCREMENT,
    `host_id`     int unsigned  not null,
    `field_ident` varchar(255)  not null,
    `field_value` varchar(1024) not null default '',
    PRIMARY KEY (`id`),
    KEY (`host_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

CREATE TABLE IF NOT EXISTS `host_import_failed_data` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `import_id` varchar(64) NOT NULL COMMENT '导入批次ID',
  `row_num` int NOT NULL COMMENT '原始CSV行号',
  `row_data` text NOT NULL COMMENT '原始行数据JSON',
  `error_msg` varchar(1024) NOT NULL COMMENT '失败原因',
  `created_at` bigint NOT NULL COMMENT '创建时间戳',
  `expires_at` bigint NOT NULL COMMENT '过期时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_import_id` (`import_id`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='';

